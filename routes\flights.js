const express = require("express");
const router = express.Router();
const Amadeus = require("amadeus");

// Initialize Amadeus client
const amadeus = new Amadeus({
  clientId: process.env.AMADEUS_CLIENT_ID,
  clientSecret: process.env.AMADEUS_CLIENT_SECRET,
  environment: process.env.AMADEUS_ENVIRONMENT || "test",
});

// Search flights
router.post("/search", async (req, res) => {
  try {
    const {
      originLocationCode,
      destinationLocationCode,
      departureDate,
      returnDate,
      adults = 1,
      children = 0,
      infants = 0,
      travelClass = "ECONOMY",
      nonStop = false,
      currencyCode = "BDT",
      max = 10,
    } = req.body;

    // Validate required fields
    if (!originLocationCode || !destinationLocationCode || !departureDate) {
      return res.status(400).json({
        error: "Missing required fields",
        required: [
          "originLocationCode",
          "destinationLocationCode",
          "departureDate",
        ],
      });
    }

    // Prepare search parameters
    const searchParams = {
      originLocationCode,
      destinationLocationCode,
      departureDate,
      adults: parseInt(adults),
      max: parseInt(max),
      currencyCode,
      travelClass,
      nonStop,
    };

    // Add return date for round trip
    if (returnDate) {
      searchParams.returnDate = returnDate;
    }

    // Add children and infants if specified
    if (children > 0) searchParams.children = parseInt(children);
    if (infants > 0) searchParams.infants = parseInt(infants);

    console.log("🔍 Searching flights with params:", searchParams);

    // Search flights using Amadeus API
    const response = await amadeus.shopping.flightOffersSearch.get(
      searchParams
    );

    // Format response for frontend
    const formattedFlights = response.data.map((offer) => ({
      id: offer.id,
      price: {
        total: offer.price.total,
        currency: offer.price.currency,
        base: offer.price.base,
        fees: offer.price.fees || [],
        taxes: offer.price.taxes || [],
      },
      itineraries: offer.itineraries.map((itinerary) => ({
        duration: itinerary.duration,
        segments: itinerary.segments.map((segment) => ({
          departure: {
            iataCode: segment.departure.iataCode,
            terminal: segment.departure.terminal,
            at: segment.departure.at,
          },
          arrival: {
            iataCode: segment.arrival.iataCode,
            terminal: segment.arrival.terminal,
            at: segment.arrival.at,
          },
          carrierCode: segment.carrierCode,
          number: segment.number,
          aircraft: segment.aircraft,
          duration: segment.duration,
          numberOfStops: segment.numberOfStops || 0,
        })),
      })),
      travelerPricings: offer.travelerPricings,
      validatingAirlineCodes: offer.validatingAirlineCodes,
      lastTicketingDate: offer.lastTicketingDate,
    }));

    res.json({
      success: true,
      data: formattedFlights,
      meta: {
        count: formattedFlights.length,
        searchParams,
      },
    });
  } catch (error) {
    console.error("❌ Flight search error:", error);

    // Handle Amadeus API errors
    if (error.response) {
      const { status } = error.response;
      const data = error.response.result || error.response.body || {};
      const errorDetail =
        data.errors?.[0]?.detail ||
        error.description?.[0]?.detail ||
        "Flight search failed";
      const errorCode = data.errors?.[0]?.code || error.description?.[0]?.code;

      return res.status(status).json({
        error: "Amadeus API Error",
        message: errorDetail,
        code: errorCode,
        retryAfter: status === 429 ? 60 : undefined,
      });
    }

    res.status(500).json({
      error: "Internal server error",
      message: "Failed to search flights",
    });
  }
});

// Get flight details
router.get("/details/:offerId", async (req, res) => {
  try {
    const { offerId } = req.params;

    if (!offerId) {
      return res.status(400).json({
        error: "Missing offer ID",
      });
    }

    // Get flight offer details
    const response = await amadeus.shopping.flightOffers.get({
      flightOfferId: offerId,
    });

    res.json({
      success: true,
      data: response.data,
    });
  } catch (error) {
    console.error("❌ Flight details error:", error);

    if (error.response) {
      const { status, data } = error.response;
      return res.status(status).json({
        error: "Amadeus API Error",
        message: data.errors?.[0]?.detail || "Failed to get flight details",
        code: data.errors?.[0]?.code,
      });
    }

    res.status(500).json({
      error: "Internal server error",
      message: "Failed to get flight details",
    });
  }
});

// Get flight price analysis
router.post("/price-analysis", async (req, res) => {
  try {
    const {
      originIataCode,
      destinationIataCode,
      departureDate,
      currencyCode = "BDT",
    } = req.body;

    if (!originIataCode || !destinationIataCode || !departureDate) {
      return res.status(400).json({
        error: "Missing required fields",
        required: ["originIataCode", "destinationIataCode", "departureDate"],
      });
    }

    // Get flight price metrics
    const response = await amadeus.analytics.itineraryPriceMetrics.get({
      originIataCode,
      destinationIataCode,
      departureDate,
      currencyCode,
    });

    res.json({
      success: true,
      data: response.data,
    });
  } catch (error) {
    console.error("❌ Price analysis error:", error);

    if (error.response) {
      const { status, data } = error.response;
      return res.status(status).json({
        error: "Amadeus API Error",
        message: data.errors?.[0]?.detail || "Price analysis failed",
        code: data.errors?.[0]?.code,
      });
    }

    res.status(500).json({
      error: "Internal server error",
      message: "Failed to analyze prices",
    });
  }
});

// Find cheapest dates
router.post("/cheapest-dates", async (req, res) => {
  try {
    const {
      origin,
      destination,
      departureDate,
      oneWay = false,
      duration,
      nonStop = false,
      viewBy = "DATE",
    } = req.body;

    if (!origin || !destination) {
      return res.status(400).json({
        error: "Missing required fields",
        required: ["origin", "destination"],
      });
    }

    const searchParams = {
      origin,
      destination,
      oneWay,
      viewBy,
      nonStop,
    };

    if (departureDate) searchParams.departureDate = departureDate;
    if (duration) searchParams.duration = duration;

    // Get cheapest flight dates
    const response = await amadeus.shopping.flightDates.get(searchParams);

    res.json({
      success: true,
      data: response.data,
    });
  } catch (error) {
    console.error("❌ Cheapest dates error:", error);

    if (error.response) {
      const { status, data } = error.response;
      return res.status(status).json({
        error: "Amadeus API Error",
        message: data.errors?.[0]?.detail || "Failed to find cheapest dates",
        code: data.errors?.[0]?.code,
      });
    }

    res.status(500).json({
      error: "Internal server error",
      message: "Failed to find cheapest dates",
    });
  }
});

module.exports = router;
