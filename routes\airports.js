const express = require("express");
const router = express.Router();
const Amadeus = require("amadeus");
const cache = require("../utils/cache");
const { searchFallbackAirports } = require("../data/fallbackAirports");

// Initialize Amadeus client
const amadeus = new Amadeus({
  clientId: process.env.AMADEUS_CLIENT_ID,
  clientSecret: process.env.AMADEUS_CLIENT_SECRET,
  environment: process.env.AMADEUS_ENVIRONMENT || "test",
});

// Search airports by keyword
router.get("/search", async (req, res) => {
  try {
    const { keyword, subType = "AIRPORT,CITY" } = req.query;

    if (!keyword) {
      return res.status(400).json({
        error: "Missing keyword parameter",
      });
    }

    // Check cache first
    const cacheKey = cache.generateKey("airports", { keyword, subType });
    const cachedResult = cache.get(cacheKey);

    if (cachedResult) {
      console.log(`📦 Cache hit for airports: ${keyword}`);
      return res.json(cachedResult);
    }

    console.log(`🔍 Searching airports for keyword: ${keyword}`);

    // Search airports using Amadeus API
    const response = await amadeus.referenceData.locations.get({
      keyword,
      subType,
      "page[limit]": 10,
    });

    // Format response for frontend
    const formattedAirports = response.data.map((location) => ({
      id: location.id,
      name: location.name,
      iataCode: location.iataCode,
      icaoCode: location.icaoCode,
      subType: location.subType,
      address: {
        cityName: location.address?.cityName,
        cityCode: location.address?.cityCode,
        countryName: location.address?.countryName,
        countryCode: location.address?.countryCode,
        regionCode: location.address?.regionCode,
      },
      geoCode: location.geoCode,
      timeZoneOffset: location.timeZoneOffset,
      analytics: location.analytics,
    }));

    const result = {
      success: true,
      data: formattedAirports,
      meta: {
        count: formattedAirports.length,
        keyword,
      },
    };

    // Cache the result
    cache.set(cacheKey, result, 10 * 60 * 1000); // Cache for 10 minutes

    res.json(result);
  } catch (error) {
    console.error("❌ Airport search error:", error);

    if (error.response) {
      const { status } = error.response;
      const data = error.response.result || error.response.body || {};
      const errorDetail =
        data.errors?.[0]?.detail ||
        error.description?.[0]?.detail ||
        "Airport search failed";
      const errorCode = data.errors?.[0]?.code || error.description?.[0]?.code;

      return res.status(status).json({
        error: "Amadeus API Error",
        message: errorDetail,
        code: errorCode,
        retryAfter: status === 429 ? 60 : undefined,
      });
    }

    // Use fallback data if API fails
    console.log("🔄 Using fallback airport data");
    const fallbackResults = searchFallbackAirports(req.query.keyword);

    const result = {
      success: true,
      data: fallbackResults,
      meta: {
        count: fallbackResults.length,
        keyword: req.query.keyword,
        source: "fallback",
      },
    };

    res.json(result);
  }
});

// Get airport details by IATA code
router.get("/:iataCode", async (req, res) => {
  try {
    const { iataCode } = req.params;

    if (!iataCode || iataCode.length !== 3) {
      return res.status(400).json({
        error: "Invalid IATA code. Must be 3 characters.",
      });
    }

    console.log(`🔍 Getting airport details for: ${iataCode}`);

    // Get airport details
    const response = await amadeus.referenceData.locations.get({
      keyword: iataCode,
      subType: "AIRPORT",
    });

    // Find exact match
    const airport = response.data.find(
      (loc) => loc.iataCode === iataCode.toUpperCase()
    );

    if (!airport) {
      return res.status(404).json({
        error: "Airport not found",
        message: `No airport found with IATA code: ${iataCode}`,
      });
    }

    const formattedAirport = {
      id: airport.id,
      name: airport.name,
      iataCode: airport.iataCode,
      icaoCode: airport.icaoCode,
      subType: airport.subType,
      address: {
        cityName: airport.address?.cityName,
        cityCode: airport.address?.cityCode,
        countryName: airport.address?.countryName,
        countryCode: airport.address?.countryCode,
        regionCode: airport.address?.regionCode,
      },
      geoCode: airport.geoCode,
      timeZoneOffset: airport.timeZoneOffset,
      analytics: airport.analytics,
    };

    res.json({
      success: true,
      data: formattedAirport,
    });
  } catch (error) {
    console.error("❌ Airport details error:", error);

    if (error.response) {
      const { status, data } = error.response;
      return res.status(status).json({
        error: "Amadeus API Error",
        message: data.errors?.[0]?.detail || "Failed to get airport details",
        code: data.errors?.[0]?.code,
      });
    }

    res.status(500).json({
      error: "Internal server error",
      message: "Failed to get airport details",
    });
  }
});

// Get popular airports by city
router.get("/city/:cityCode", async (req, res) => {
  try {
    const { cityCode } = req.params;

    if (!cityCode) {
      return res.status(400).json({
        error: "Missing city code parameter",
      });
    }

    console.log(`🔍 Getting airports for city: ${cityCode}`);

    // Search airports in the city
    const response = await amadeus.referenceData.locations.get({
      keyword: cityCode,
      subType: "AIRPORT,CITY",
      "page[limit]": 20,
    });

    // Filter airports in the specified city
    const cityAirports = response.data.filter(
      (location) =>
        location.address?.cityCode === cityCode.toUpperCase() ||
        location.iataCode === cityCode.toUpperCase()
    );

    const formattedAirports = cityAirports.map((airport) => ({
      id: airport.id,
      name: airport.name,
      iataCode: airport.iataCode,
      icaoCode: airport.icaoCode,
      subType: airport.subType,
      address: {
        cityName: airport.address?.cityName,
        cityCode: airport.address?.cityCode,
        countryName: airport.address?.countryName,
        countryCode: airport.address?.countryCode,
        regionCode: airport.address?.regionCode,
      },
      geoCode: airport.geoCode,
      timeZoneOffset: airport.timeZoneOffset,
      analytics: airport.analytics,
    }));

    res.json({
      success: true,
      data: formattedAirports,
      meta: {
        count: formattedAirports.length,
        cityCode,
      },
    });
  } catch (error) {
    console.error("❌ City airports error:", error);

    if (error.response) {
      const { status, data } = error.response;
      return res.status(status).json({
        error: "Amadeus API Error",
        message: data.errors?.[0]?.detail || "Failed to get city airports",
        code: data.errors?.[0]?.code,
      });
    }

    res.status(500).json({
      error: "Internal server error",
      message: "Failed to get city airports",
    });
  }
});

module.exports = router;
