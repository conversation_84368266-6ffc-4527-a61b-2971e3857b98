.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
    url("https://images.unsplash.com/photo-1531058020387-3be344556be6?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80");
  background-size: cover;
  background-position: center;
  color: white;
  padding: 100px 0;
}

.visa-type-card {
  transition: transform 0.3s;
  border: none;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.visa-type-card:hover {
  transform: translateY(-10px);
}

.visa-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #0d6efd;
}

.process-step {
  position: relative;
  padding-left: 50px;
  margin-bottom: 30px;
}

.step-number {
  position: absolute;
  left: 0;
  top: 0;
  width: 40px;
  height: 40px;
  background-color: #0d6efd;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.contact-card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.footer {
  background-color: #2c3e50;
  color: white;
  padding-top: 3rem;
  padding-bottom: 1rem;
}

.footer a {
  color: #adb5bd;
  text-decoration: none;
  transition: color 0.2s;
}

.footer a:hover {
  color: white;
}
