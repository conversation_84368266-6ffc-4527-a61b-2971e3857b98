/**
 * Flight Results Display Manager
 * Handles dynamic display of flight search results
 */

class FlightResultsManager {
  constructor() {
    this.resultsContainer = null;
    this.init();
  }

  init() {
    this.createResultsContainer();
  }

  createResultsContainer() {
    // Find the existing results section or create one
    this.resultsContainer = document.querySelector("#flight-results-container");

    if (!this.resultsContainer) {
      // Create results container after the booking form
      const bookingSection = document.querySelector("#booking-form");
      if (bookingSection) {
        this.resultsContainer = document.createElement("section");
        this.resultsContainer.id = "flight-results-container";
        this.resultsContainer.className = "py-5 bg-light";
        this.resultsContainer.style.display = "none";
        bookingSection.insertAdjacentElement("afterend", this.resultsContainer);
      }
    }
  }

  displayResults(flights, searchParams = {}) {
    if (!this.resultsContainer) {
      console.error("Results container not found");
      return;
    }

    if (!flights || flights.length === 0) {
      this.displayNoResults(searchParams);
      return;
    }

    this.resultsContainer.innerHTML = this.generateResultsHTML(
      flights,
      searchParams
    );
    this.resultsContainer.style.display = "block";
    this.setupResultsEventListeners();

    // Scroll to results
    this.resultsContainer.scrollIntoView({ behavior: "smooth" });
  }

  displayNoResults(searchParams) {
    this.resultsContainer.innerHTML = `
      <div class="container">
        <div class="text-center py-5">
          <i class="fas fa-search text-muted mb-4" style="font-size: 4rem;"></i>
          <h3 class="text-muted mb-3">No Flights Found</h3>
          <p class="text-muted mb-4">
            We couldn't find any flights for your search criteria. 
            Try adjusting your dates or destinations.
          </p>
          <button class="btn btn-primary" onclick="document.querySelector('#booking-form').scrollIntoView({behavior: 'smooth'})">
            <i class="fas fa-arrow-up me-2"></i>Modify Search
          </button>
        </div>
      </div>
    `;
    this.resultsContainer.style.display = "block";
  }

  generateResultsHTML(flights, searchParams) {
    const resultsCount = flights.length;
    const route = this.formatRoute(searchParams);
    const isRoundTrip =
      searchParams.tripType === "round-trip" && searchParams.returnDate;

    return `
      <div class="container">
        <div class="text-center mb-5">
          <span class="badge bg-primary px-3 py-2 rounded-pill mb-3">
            <i class="fas fa-plane me-2"></i>Flight Results
          </span>
          <h2 class="fw-bold display-5 mb-4">
            Found <span class="text-primary">${resultsCount}</span> Flights
          </h2>
          <p class="lead text-muted">
            ${route} • ${this.formatDate(searchParams.departureDate)}
            ${
              searchParams.returnDate
                ? ` - ${this.formatDate(searchParams.returnDate)}`
                : ""
            }
            ${
              searchParams.tripType
                ? ` • ${this.formatTripType(searchParams.tripType)}`
                : ""
            }
          </p>
        </div>

        ${
          isRoundTrip
            ? this.generateRoundTripLayout(flights, searchParams)
            : this.generateOneWayLayout(flights, searchParams)
        }

        <div class="text-center mt-5">
          <button class="btn btn-outline-primary btn-lg px-5 py-3 rounded-pill" onclick="document.querySelector('#booking-form').scrollIntoView({behavior: 'smooth'})">
            <i class="fas fa-search me-2"></i>Search Again
          </button>
        </div>
      </div>
    `;
  }

  generateRoundTripLayout(flights, searchParams) {
    // Group flights by outbound and return
    const outboundFlights = flights.filter(
      (flight) =>
        flight.itineraries.length === 2 || flight.itineraries.length === 1
    );

    return `
      <div class="row g-4">
        <!-- Outbound Flights -->
        <div class="col-lg-6">
          <div class="flight-direction-header bg-primary text-white p-3 rounded-top">
            <h5 class="mb-0">
              <i class="fas fa-plane-departure me-2"></i>
              Outbound Flights
            </h5>
            <small>${searchParams.originLocationCode} → ${
      searchParams.destinationLocationCode
    }</small>
          </div>
          <div class="flight-direction-content bg-light p-3 rounded-bottom">
            ${outboundFlights
              .slice(0, 5)
              .map((flight, index) =>
                this.generateCompactFlightCard(flight, index, "outbound")
              )
              .join("")}
          </div>
        </div>

        <!-- Return Flights -->
        <div class="col-lg-6">
          <div class="flight-direction-header bg-success text-white p-3 rounded-top">
            <h5 class="mb-0">
              <i class="fas fa-plane-arrival me-2"></i>
              Return Flights
            </h5>
            <small>${searchParams.destinationLocationCode} → ${
      searchParams.originLocationCode
    }</small>
          </div>
          <div class="flight-direction-content bg-light p-3 rounded-bottom">
            ${outboundFlights
              .slice(0, 5)
              .map((flight, index) =>
                this.generateCompactFlightCard(flight, index, "return")
              )
              .join("")}
          </div>
        </div>
      </div>

      <!-- Combined Flight Options -->
      <div class="mt-5">
        <h4 class="text-center mb-4">
          <i class="fas fa-layer-group me-2"></i>Complete Round Trip Options
        </h4>
        <div class="row g-4">
          ${flights
            .slice(0, 6)
            .map((flight, index) => this.generateFlightCard(flight, index))
            .join("")}
        </div>
      </div>
    `;
  }

  generateOneWayLayout(flights, searchParams) {
    return `
      <div class="row g-4">
        ${flights
          .map((flight, index) => this.generateFlightCard(flight, index))
          .join("")}
      </div>
    `;
  }

  generateCompactFlightCard(flight, index, direction) {
    const itinerary =
      direction === "return" && flight.itineraries[1]
        ? flight.itineraries[1]
        : flight.itineraries[0];
    const segment = itinerary.segments[0];
    const lastSegment = itinerary.segments[itinerary.segments.length - 1];

    const departureTime = this.formatTime(segment.departure.at);
    const arrivalTime = this.formatTime(lastSegment.arrival.at);
    const duration = this.formatDuration(itinerary.duration);
    const stops = itinerary.segments.length - 1;

    const price = parseFloat(flight.price.total);
    const currency = flight.price.currency;

    const airlineCode = segment.carrierCode;
    const airlineName = this.getAirlineName(airlineCode);

    return `
      <div class="compact-flight-card bg-white rounded-3 shadow-sm border mb-3 p-3" data-flight-id="${
        flight.id
      }">
        <div class="row align-items-center">
          <div class="col-md-8">
            <div class="d-flex align-items-center mb-2">
              <div class="airline-logo-placeholder bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                <i class="fas fa-plane text-primary small"></i>
              </div>
              <div>
                <small class="fw-bold">${airlineName}</small>
                <br>
                <small class="text-muted">${airlineCode} ${
      segment.number
    }</small>
              </div>
            </div>

            <div class="flight-route-compact d-flex align-items-center">
              <div class="text-center">
                <div class="fw-bold">${departureTime}</div>
                <small class="text-muted">${segment.departure.iataCode}</small>
              </div>
              <div class="flex-grow-1 mx-3">
                <div class="flight-line bg-secondary" style="height: 1px; width: 100%; position: relative;">
                  <i class="fas fa-plane text-secondary position-absolute top-50 start-50 translate-middle bg-white px-1" style="font-size: 10px;"></i>
                </div>
                <div class="text-center">
                  <small class="text-muted">${duration}</small>
                </div>
              </div>
              <div class="text-center">
                <div class="fw-bold">${arrivalTime}</div>
                <small class="text-muted">${
                  lastSegment.arrival.iataCode
                }</small>
              </div>
            </div>

            <div class="mt-2">
              <small class="text-${stops === 0 ? "success" : "warning"}">
                ${
                  stops === 0
                    ? "Non-stop"
                    : `${stops} stop${stops > 1 ? "s" : ""}`
                }
              </small>
            </div>
          </div>

          <div class="col-md-4 text-end">
            <div class="price-display fw-bold text-primary">${this.formatPrice(
              price,
              currency
            )}</div>
            <small class="text-muted">per person</small>
            <div class="mt-2">
              <button class="btn btn-sm btn-outline-primary compact-select-btn" data-flight-id="${
                flight.id
              }" data-direction="${direction}">
                Select
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  formatTripType(tripType) {
    const types = {
      "round-trip": "Round Trip",
      "one-way": "One Way",
      "multi-city": "Multi City",
    };
    return types[tripType] || tripType;
  }

  generateFlightCard(flight, index) {
    const mainItinerary = flight.itineraries[0];
    const outboundSegment = mainItinerary.segments[0];
    const lastSegment =
      mainItinerary.segments[mainItinerary.segments.length - 1];

    const departureTime = this.formatTime(outboundSegment.departure.at);
    const arrivalTime = this.formatTime(lastSegment.arrival.at);
    const duration = this.formatDuration(mainItinerary.duration);
    const stops = mainItinerary.segments.length - 1;

    const price = parseFloat(flight.price.total);
    const currency = flight.price.currency;

    // Get airline info
    const airlineCode = outboundSegment.carrierCode;
    const airlineName = this.getAirlineName(airlineCode);
    const flightNumber = `${airlineCode} ${outboundSegment.number}`;

    return `
      <div class="col-lg-6">
        <div class="flight-card bg-white rounded-4 shadow-lg border-0 overflow-hidden h-100" data-flight-id="${
          flight.id
        }">
          <div class="card-body p-4">
            <!-- Airline Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
              <div class="d-flex align-items-center">
                <div class="airline-logo-placeholder bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                  <i class="fas fa-plane text-primary"></i>
                </div>
                <div>
                  <h6 class="mb-0 fw-bold">${airlineName}</h6>
                  <small class="text-muted">${flightNumber}</small>
                </div>
              </div>
              <div class="text-end">
                <div class="price-display fw-bold text-primary fs-4">
                  ${this.formatPrice(price, currency)}
                </div>
                <small class="text-muted">per person</small>
              </div>
            </div>

            <!-- Flight Route -->
            <div class="flight-route mb-4">
              <div class="row align-items-center text-center">
                <div class="col-4">
                  <div class="departure-info">
                    <div class="time-display fw-bold fs-3 text-dark">${departureTime}</div>
                    <div class="location text-muted">${
                      outboundSegment.departure.iataCode
                    }</div>
                    <small class="text-muted">${this.formatDate(
                      outboundSegment.departure.at
                    )}</small>
                  </div>
                </div>
                <div class="col-4">
                  <div class="flight-path position-relative">
                    <div class="flight-line bg-primary" style="height: 2px; width: 100%;"></div>
                    <i class="fas fa-plane text-primary position-absolute top-50 start-50 translate-middle bg-white px-2"></i>
                    <div class="duration text-muted small mt-2">${duration}</div>
                    <small class="text-${stops === 0 ? "success" : "warning"}">
                      ${
                        stops === 0
                          ? "Non-stop"
                          : `${stops} stop${stops > 1 ? "s" : ""}`
                      }
                    </small>
                  </div>
                </div>
                <div class="col-4">
                  <div class="arrival-info">
                    <div class="time-display fw-bold fs-3 text-dark">${arrivalTime}</div>
                    <div class="location text-muted">${
                      lastSegment.arrival.iataCode
                    }</div>
                    <small class="text-muted">${this.formatDate(
                      lastSegment.arrival.at
                    )}</small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Flight Details -->
            <div class="flight-details mb-4">
              <div class="row g-3">
                <div class="col-6">
                  <div class="detail-item d-flex align-items-center">
                    <i class="fas fa-suitcase text-primary me-2"></i>
                    <span class="small">Baggage Included</span>
                  </div>
                </div>
                <div class="col-6">
                  <div class="detail-item d-flex align-items-center">
                    <i class="fas fa-utensils text-success me-2"></i>
                    <span class="small">Meal Included</span>
                  </div>
                </div>
                <div class="col-6">
                  <div class="detail-item d-flex align-items-center">
                    <i class="fas fa-chair text-warning me-2"></i>
                    <span class="small">${
                      flight.travelerPricings[0]?.fareDetailsBySegment[0]
                        ?.cabin || "Economy"
                    }</span>
                  </div>
                </div>
                <div class="col-6">
                  <div class="detail-item d-flex align-items-center">
                    <i class="fas fa-clock text-info me-2"></i>
                    <span class="small">${duration}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex gap-2">
              <button class="btn btn-outline-primary flex-fill flight-details-btn" data-flight-id="${
                flight.id
              }">
                <i class="fas fa-info-circle me-2"></i>Details
              </button>
              <button class="btn btn-primary flex-fill flight-book-btn" data-flight-id="${
                flight.id
              }">
                <i class="fas fa-ticket-alt me-2"></i>Book Now
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  setupResultsEventListeners() {
    // Flight details buttons
    const detailsButtons = this.resultsContainer.querySelectorAll(
      ".flight-details-btn"
    );
    detailsButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        const flightId = e.target.closest("button").dataset.flightId;
        this.showFlightDetails(flightId);
      });
    });

    // Book now buttons
    const bookButtons =
      this.resultsContainer.querySelectorAll(".flight-book-btn");
    bookButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        const flightId = e.target.closest("button").dataset.flightId;
        this.handleBookFlight(flightId);
      });
    });

    // Card hover effects
    const flightCards = this.resultsContainer.querySelectorAll(".flight-card");
    flightCards.forEach((card) => {
      card.addEventListener("mouseenter", () => {
        card.style.transform = "translateY(-5px)";
        card.style.boxShadow = "0 15px 35px rgba(0,0,0,0.1)";
      });

      card.addEventListener("mouseleave", () => {
        card.style.transform = "translateY(0)";
        card.style.boxShadow = "";
      });
    });
  }

  showFlightDetails(flightId) {
    // Find flight data
    const flight = window.flightSearchManager?.searchResults?.find(
      (f) => f.id === flightId
    );

    if (!flight) {
      console.error("Flight not found:", flightId);
      return;
    }

    // Create modal or detailed view
    this.createFlightDetailsModal(flight);
  }

  createFlightDetailsModal(flight) {
    // Create modal HTML
    const modalHTML = `
      <div class="modal fade" id="flightDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="fas fa-plane me-2"></i>Flight Details
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              ${this.generateDetailedFlightInfo(flight)}
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" onclick="flightResultsManager.handleBookFlight('${
                flight.id
              }')">
                <i class="fas fa-ticket-alt me-2"></i>Book This Flight
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById("flightDetailsModal");
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML("beforeend", modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(
      document.getElementById("flightDetailsModal")
    );
    modal.show();
  }

  generateDetailedFlightInfo(flight) {
    // Generate detailed flight information
    return `
      <div class="flight-details-content">
        <h6 class="fw-bold mb-3">Price Breakdown</h6>
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="bg-light p-3 rounded">
              <div class="d-flex justify-content-between">
                <span>Base Price:</span>
                <span class="fw-bold">${this.formatPrice(
                  flight.price.base,
                  flight.price.currency
                )}</span>
              </div>
              <div class="d-flex justify-content-between">
                <span>Taxes & Fees:</span>
                <span class="fw-bold">${this.formatPrice(
                  parseFloat(flight.price.total) -
                    parseFloat(flight.price.base),
                  flight.price.currency
                )}</span>
              </div>
              <hr>
              <div class="d-flex justify-content-between">
                <span class="fw-bold">Total:</span>
                <span class="fw-bold text-primary">${this.formatPrice(
                  flight.price.total,
                  flight.price.currency
                )}</span>
              </div>
            </div>
          </div>
        </div>

        <h6 class="fw-bold mb-3">Flight Itinerary</h6>
        ${flight.itineraries
          .map(
            (itinerary, index) => `
          <div class="itinerary-section mb-4">
            <h6 class="text-primary">${
              index === 0 ? "Outbound" : "Return"
            } Journey</h6>
            ${itinerary.segments
              .map(
                (segment) => `
              <div class="segment-info bg-light p-3 rounded mb-2">
                <div class="row">
                  <div class="col-md-6">
                    <strong>${segment.departure.iataCode}</strong> → <strong>${
                  segment.arrival.iataCode
                }</strong>
                    <br>
                    <small class="text-muted">${segment.carrierCode} ${
                  segment.number
                }</small>
                  </div>
                  <div class="col-md-6 text-md-end">
                    <div>${this.formatTime(
                      segment.departure.at
                    )} - ${this.formatTime(segment.arrival.at)}</div>
                    <small class="text-muted">${this.formatDuration(
                      segment.duration
                    )}</small>
                  </div>
                </div>
              </div>
            `
              )
              .join("")}
          </div>
        `
          )
          .join("")}
      </div>
    `;
  }

  handleBookFlight(flightId) {
    // Handle flight booking
    console.log("Booking flight:", flightId);

    // For now, show a simple alert
    alert(
      `Flight booking functionality will be implemented soon!\nFlight ID: ${flightId}`
    );

    // Close modal if open
    const modal = bootstrap.Modal.getInstance(
      document.getElementById("flightDetailsModal")
    );
    if (modal) {
      modal.hide();
    }
  }

  // Utility functions
  formatRoute(searchParams) {
    return `${searchParams.originLocationCode || "Origin"} → ${
      searchParams.destinationLocationCode || "Destination"
    }`;
  }

  formatDate(dateString) {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  }

  formatTime(dateTimeString) {
    if (!dateTimeString) return "";
    const date = new Date(dateTimeString);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  }

  formatDuration(duration) {
    if (!duration) return "";
    // Parse ISO 8601 duration (PT2H30M)
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?/);
    if (!match) return duration;

    const hours = match[1] ? parseInt(match[1]) : 0;
    const minutes = match[2] ? parseInt(match[2]) : 0;

    if (hours && minutes) {
      return `${hours}h ${minutes}m`;
    } else if (hours) {
      return `${hours}h`;
    } else if (minutes) {
      return `${minutes}m`;
    }
    return duration;
  }

  formatPrice(amount, currency = "BDT") {
    const numAmount = parseFloat(amount);
    if (currency === "BDT") {
      return `৳ ${numAmount.toLocaleString()}`;
    }
    return `${currency} ${numAmount.toLocaleString()}`;
  }

  getAirlineName(airlineCode) {
    const airlines = {
      BG: "Biman Bangladesh",
      EK: "Emirates",
      QR: "Qatar Airways",
      EY: "Etihad Airways",
      TK: "Turkish Airlines",
      SQ: "Singapore Airlines",
      TG: "Thai Airways",
      MH: "Malaysia Airlines",
      AI: "Air India",
      UK: "Vistara",
      BS: "US-Bangla Airlines",
    };
    return airlines[airlineCode] || airlineCode;
  }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.flightResultsManager = new FlightResultsManager();

  // Extend FlightSearchManager to use results manager
  if (window.flightSearchManager) {
    const originalDisplayResults =
      window.flightSearchManager.displaySearchResults;
    window.flightSearchManager.displaySearchResults = function (
      flights,
      searchParams
    ) {
      window.flightResultsManager.displayResults(flights, searchParams);
    };
  }
});

// Export for use in other scripts
window.FlightResultsManager = FlightResultsManager;
