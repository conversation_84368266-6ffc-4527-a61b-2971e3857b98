/**
 * Fallback airport data for when Amadeus API is unavailable
 * Contains popular airports in Bangladesh and common destinations
 */

const fallbackAirports = [
  // Bangladesh Airports
  {
    id: "DAC",
    name: "<PERSON><PERSON>rat <PERSON> International Airport",
    iataCode: "DAC",
    icaoCode: "VGHS",
    subType: "AIRPORT",
    address: {
      cityName: "Dhaka",
      cityCode: "DAC",
      countryName: "Bangladesh",
      countryCode: "BD",
      regionCode: "ASIA"
    },
    geoCode: {
      latitude: 23.8431,
      longitude: 90.3978
    }
  },
  {
    id: "CGP",
    name: "Shah Amanat International Airport",
    iataCode: "CGP",
    icaoCode: "VGEG",
    subType: "AIRPORT",
    address: {
      cityName: "Chittagong",
      cityCode: "CGP",
      countryName: "Bangladesh",
      countryCode: "BD",
      regionCode: "ASIA"
    },
    geoCode: {
      latitude: 22.2496,
      longitude: 91.8133
    }
  },
  {
    id: "SPD",
    name: "Saidpur Airport",
    iataCode: "SPD",
    icaoCode: "VGSD",
    subType: "AIRPORT",
    address: {
      cityName: "Saidpur",
      cityCode: "SPD",
      countryName: "Bangladesh",
      countryCode: "BD",
      regionCode: "ASIA"
    },
    geoCode: {
      latitude: 25.7592,
      longitude: 88.9089
    }
  },

  // Popular International Destinations
  {
    id: "DXB",
    name: "Dubai International Airport",
    iataCode: "DXB",
    icaoCode: "OMDB",
    subType: "AIRPORT",
    address: {
      cityName: "Dubai",
      cityCode: "DXB",
      countryName: "United Arab Emirates",
      countryCode: "AE",
      regionCode: "MIDDLE_EAST"
    },
    geoCode: {
      latitude: 25.2532,
      longitude: 55.3657
    }
  },
  {
    id: "DOH",
    name: "Hamad International Airport",
    iataCode: "DOH",
    icaoCode: "OTHH",
    subType: "AIRPORT",
    address: {
      cityName: "Doha",
      cityCode: "DOH",
      countryName: "Qatar",
      countryCode: "QA",
      regionCode: "MIDDLE_EAST"
    },
    geoCode: {
      latitude: 25.2731,
      longitude: 51.6080
    }
  },
  {
    id: "KUL",
    name: "Kuala Lumpur International Airport",
    iataCode: "KUL",
    icaoCode: "WMKK",
    subType: "AIRPORT",
    address: {
      cityName: "Kuala Lumpur",
      cityCode: "KUL",
      countryName: "Malaysia",
      countryCode: "MY",
      regionCode: "ASIA"
    },
    geoCode: {
      latitude: 2.7456,
      longitude: 101.7072
    }
  },
  {
    id: "BKK",
    name: "Suvarnabhumi Airport",
    iataCode: "BKK",
    icaoCode: "VTBS",
    subType: "AIRPORT",
    address: {
      cityName: "Bangkok",
      cityCode: "BKK",
      countryName: "Thailand",
      countryCode: "TH",
      regionCode: "ASIA"
    },
    geoCode: {
      latitude: 13.6900,
      longitude: 100.7501
    }
  },
  {
    id: "SIN",
    name: "Singapore Changi Airport",
    iataCode: "SIN",
    icaoCode: "WSSS",
    subType: "AIRPORT",
    address: {
      cityName: "Singapore",
      cityCode: "SIN",
      countryName: "Singapore",
      countryCode: "SG",
      regionCode: "ASIA"
    },
    geoCode: {
      latitude: 1.3644,
      longitude: 103.9915
    }
  },
  {
    id: "DEL",
    name: "Indira Gandhi International Airport",
    iataCode: "DEL",
    icaoCode: "VIDP",
    subType: "AIRPORT",
    address: {
      cityName: "New Delhi",
      cityCode: "DEL",
      countryName: "India",
      countryCode: "IN",
      regionCode: "ASIA"
    },
    geoCode: {
      latitude: 28.5562,
      longitude: 77.1000
    }
  },
  {
    id: "IST",
    name: "Istanbul Airport",
    iataCode: "IST",
    icaoCode: "LTFM",
    subType: "AIRPORT",
    address: {
      cityName: "Istanbul",
      cityCode: "IST",
      countryName: "Turkey",
      countryCode: "TR",
      regionCode: "EUROPE"
    },
    geoCode: {
      latitude: 41.2619,
      longitude: 28.7279
    }
  },
  {
    id: "LHR",
    name: "London Heathrow Airport",
    iataCode: "LHR",
    icaoCode: "EGLL",
    subType: "AIRPORT",
    address: {
      cityName: "London",
      cityCode: "LON",
      countryName: "United Kingdom",
      countryCode: "GB",
      regionCode: "EUROPE"
    },
    geoCode: {
      latitude: 51.4700,
      longitude: -0.4543
    }
  }
];

/**
 * Search airports in fallback data
 */
function searchFallbackAirports(keyword) {
  if (!keyword || keyword.length < 2) {
    return [];
  }

  const searchTerm = keyword.toLowerCase();
  
  return fallbackAirports.filter(airport => {
    return (
      airport.name.toLowerCase().includes(searchTerm) ||
      airport.iataCode.toLowerCase().includes(searchTerm) ||
      airport.address.cityName.toLowerCase().includes(searchTerm) ||
      airport.address.countryName.toLowerCase().includes(searchTerm)
    );
  });
}

module.exports = {
  fallbackAirports,
  searchFallbackAirports
};
