{"version": 3, "file": "queryAnalyzer.js", "sourceRoot": "", "sources": ["../src/queryAnalyzer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAExB,wCAAwC;AACxC,MAAM,CAAN,IAAY,eAKX;AALD,WAAY,eAAe;IACzB,8CAA2B,CAAA;IAC3B,oDAAiC,CAAA;IACjC,4CAAyB,CAAA;IACzB,oDAAiC,CAAA;AACnC,CAAC,EALW,eAAe,GAAf,eAAe,KAAf,eAAe,QAK1B;AAED,8BAA8B;AAC9B,MAAM,CAAN,IAAY,SAQX;AARD,WAAY,SAAS;IACnB,4CAA+B,CAAA;IAC/B,sCAAyB,CAAA;IACzB,4BAAe,CAAA;IACf,8BAAiB,CAAA;IACjB,gCAAmB,CAAA;IACnB,kCAAqB,CAAA;IACrB,kCAAqB,CAAA;AACvB,CAAC,EARW,SAAS,GAAT,SAAS,KAAT,SAAS,QAQpB;AAED,8BAA8B;AAC9B,MAAM,CAAN,IAAY,iBAOX;AAPD,WAAY,iBAAiB;IAC3B,kCAAa,CAAA;IACb,kCAAa,CAAA;IACb,0CAAqB,CAAA;IACrB,kCAAa,CAAA;IACb,oCAAe,CAAA;IACf,gCAAW,CAAA;AACb,CAAC,EAPW,iBAAiB,GAAjB,iBAAiB,KAAjB,iBAAiB,QAO5B;AAuDD,4BAA4B;AAC5B,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,CAAC,MAAM,CAAC;IAC1C,IAAI,EAAE,CAAC,CAAC,UAAU,CAAC,eAAe,CAAC;IACnC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;QAClB,IAAI,EAAE,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC;QAC7B,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,UAAU,EAAE,CAAC,CAAC,OAAO,EAAE;KACxB,CAAC;IACF,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC;QACf,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;QACf,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;QACtD,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC3B,UAAU,EAAE,CAAC,CAAC,OAAO,EAAE;QACvB,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KAC/B,CAAC,CAAC,QAAQ,EAAE;IACb,YAAY,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7B,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;QACf,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;QACtD,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC3B,UAAU,EAAE,CAAC,CAAC,OAAO,EAAE;QACvB,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KAC/B,CAAC,CAAC;IACH,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC;QACjB,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QACrD,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE;KAC3B,CAAC,CAAC,QAAQ,EAAE;IACb,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC;QACf,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE;QAClB,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE;QACpB,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;QACnD,UAAU,EAAE,CAAC,CAAC,OAAO,EAAE;QACvB,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KAC/B,CAAC,CAAC,QAAQ,EAAE;IACb,OAAO,EAAE,CAAC,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,QAAQ,EAAE;IACnD,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC;QACpB,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE;QAC1E,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,iBAAiB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE;QAC7E,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE;QAClE,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC1C,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;KAC9C,CAAC,CAAC,QAAQ,EAAE;IACb,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE;IACpB,UAAU,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACpC,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;CAC5C,CAAC,CAAC;AAEH,yCAAyC;AACzC,MAAM,UAAU,iBAAiB,CAAC,KAAa;IAC7C,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IAE3C,uBAAuB;IACvB,IAAI,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QACzC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC;QAClC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;QACxC,OAAO,eAAe,CAAC,WAAW,CAAC;KACpC;IAED,sBAAsB;IACtB,IAAI,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC;QAChC,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC;QAC1C,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;QAClD,OAAO,eAAe,CAAC,UAAU,CAAC;KACnC;IAED,0BAA0B;IAC1B,IAAI,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC;QACxC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC;QACpC,cAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;QAC1C,OAAO,eAAe,CAAC,cAAc,CAAC;KACvC;IAED,4BAA4B;IAC5B,OAAO,eAAe,CAAC,cAAc,CAAC;AACxC,CAAC;AAED,wCAAwC;AACxC,MAAM,UAAU,gBAAgB,CAAC,KAAa;IAK5C,qEAAqE;IACrE,OAAO;QACL,IAAI,EAAE,SAAS,CAAC,QAAQ;QACxB,KAAK,EAAE,EAAE;QACT,UAAU,EAAE,IAAI;KACjB,CAAC;AACJ,CAAC;AAED,uCAAuC;AACvC,MAAM,UAAU,gBAAgB,CAAC,KAAa;IAI5C,mEAAmE;IACnE,OAAO;QACL,YAAY,EAAE,EAAE;KACjB,CAAC;AACJ,CAAC;AAED,yBAAyB;AACzB,MAAM,CAAC,KAAK,UAAU,YAAY,CAAC,KAAa;IAC9C,MAAM,IAAI,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACtC,MAAM,SAAS,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC1C,MAAM,SAAS,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAE1C,MAAM,QAAQ,GAAkB;QAC9B,IAAI;QACJ,SAAS;QACT,GAAG,SAAS;QACZ,QAAQ,EAAE,KAAK;QACf,UAAU,EAAE,GAAG,EAAE,0DAA0D;KAC5E,CAAC;IAEF,wBAAwB;IACxB,mBAAmB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAEpC,OAAO,QAAQ,CAAC;AAClB,CAAC"}