/**
 * Multi-City Flight Search Manager
 * Handles dynamic addition/removal of flight segments
 */

class MultiCityManager {
  constructor() {
    this.segmentCount = 3; // Start with 3 segments
    this.maxSegments = 6;
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.setupAirportAutocomplete();
  }

  setupEventListeners() {
    // Add segment button
    const addButton = document.getElementById('add-segment');
    if (addButton) {
      addButton.addEventListener('click', () => {
        this.addSegment();
      });
    }

    // Remove segment buttons (delegated event)
    document.addEventListener('click', (e) => {
      if (e.target.closest('.remove-segment')) {
        this.removeSegment(e.target.closest('.multi-city-segment'));
      }
    });
  }

  setupAirportAutocomplete() {
    // This will be handled by the main FlightSearchManager
    // Just ensure new inputs get the autocomplete functionality
    document.addEventListener('DOMContentLoaded', () => {
      if (window.flightSearchManager) {
        window.flightSearchManager.setupAirportAutocomplete();
      }
    });
  }

  addSegment() {
    if (this.segmentCount >= this.maxSegments) {
      alert(`Maximum ${this.maxSegments} segments allowed`);
      return;
    }

    this.segmentCount++;
    const segmentsContainer = document.getElementById('multi-city-segments');
    
    const colors = ['primary', 'success', 'info', 'warning', 'danger', 'secondary'];
    const colorIndex = (this.segmentCount - 1) % colors.length;
    const color = colors[colorIndex];

    const newSegment = document.createElement('div');
    newSegment.className = 'multi-city-segment border rounded-3 p-4 mb-4';
    newSegment.style.background = '#f8f9fa';
    
    newSegment.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0 fw-bold text-${color}">
          <i class="fas fa-plane me-2"></i>Flight ${this.segmentCount}
        </h6>
        <button type="button" class="btn btn-sm btn-outline-danger remove-segment">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="row g-3">
        <div class="col-lg-4">
          <label class="form-label fw-semibold mb-2">
            <i class="fas fa-plane-departure text-primary me-2"></i>From
          </label>
          <input type="text" class="form-control airport-input" placeholder="City or Airport" />
        </div>
        <div class="col-lg-4">
          <label class="form-label fw-semibold mb-2">
            <i class="fas fa-plane-arrival text-success me-2"></i>To
          </label>
          <input type="text" class="form-control airport-input" placeholder="City or Airport" />
        </div>
        <div class="col-lg-4">
          <label class="form-label fw-semibold mb-2">
            <i class="fas fa-calendar text-warning me-2"></i>Date
          </label>
          <input type="date" class="form-control" />
        </div>
      </div>
    `;

    segmentsContainer.appendChild(newSegment);

    // Setup autocomplete for new inputs
    this.setupAutocompleteForNewInputs(newSegment);

    // Update add button state
    this.updateAddButtonState();

    // Animate the new segment
    newSegment.style.opacity = '0';
    newSegment.style.transform = 'translateY(20px)';
    setTimeout(() => {
      newSegment.style.transition = 'all 0.3s ease';
      newSegment.style.opacity = '1';
      newSegment.style.transform = 'translateY(0)';
    }, 10);
  }

  removeSegment(segmentElement) {
    if (this.segmentCount <= 2) {
      alert('Minimum 2 segments required for multi-city');
      return;
    }

    // Animate removal
    segmentElement.style.transition = 'all 0.3s ease';
    segmentElement.style.opacity = '0';
    segmentElement.style.transform = 'translateY(-20px)';
    
    setTimeout(() => {
      segmentElement.remove();
      this.segmentCount--;
      this.updateSegmentNumbers();
      this.updateAddButtonState();
    }, 300);
  }

  updateSegmentNumbers() {
    const segments = document.querySelectorAll('.multi-city-segment');
    const colors = ['primary', 'success', 'info', 'warning', 'danger', 'secondary'];
    
    segments.forEach((segment, index) => {
      const header = segment.querySelector('h6');
      const colorIndex = index % colors.length;
      const color = colors[colorIndex];
      
      header.className = `mb-0 fw-bold text-${color}`;
      header.innerHTML = `<i class="fas fa-plane me-2"></i>Flight ${index + 1}`;
    });
  }

  updateAddButtonState() {
    const addButton = document.getElementById('add-segment');
    if (addButton) {
      if (this.segmentCount >= this.maxSegments) {
        addButton.disabled = true;
        addButton.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Maximum Segments Reached';
      } else {
        addButton.disabled = false;
        addButton.innerHTML = '<i class="fas fa-plus me-2"></i>Add Another Flight';
      }
    }
  }

  setupAutocompleteForNewInputs(container) {
    const airportInputs = container.querySelectorAll('.airport-input');
    
    airportInputs.forEach(input => {
      let debounceTimer;
      
      input.addEventListener('input', (e) => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
          if (window.flightSearchManager) {
            window.flightSearchManager.handleAirportSearch(e.target);
          }
        }, 300);
      });

      input.addEventListener('focus', () => {
        if (window.flightSearchManager) {
          window.flightSearchManager.showAirportDropdown(input);
        }
      });

      input.addEventListener('blur', () => {
        setTimeout(() => {
          if (window.flightSearchManager) {
            window.flightSearchManager.hideAirportDropdown(input);
          }
        }, 200);
      });
    });
  }

  getMultiCityData() {
    const segments = document.querySelectorAll('.multi-city-segment');
    const segmentData = [];

    segments.forEach((segment, index) => {
      const fromInput = segment.querySelector('input[placeholder*="From"], .airport-input:first-of-type');
      const toInput = segment.querySelector('input[placeholder*="To"], .airport-input:last-of-type');
      const dateInput = segment.querySelector('input[type="date"]');

      if (fromInput && toInput && dateInput) {
        segmentData.push({
          segmentNumber: index + 1,
          originLocationCode: fromInput.dataset.iataCode || this.extractIataCode(fromInput.value),
          destinationLocationCode: toInput.dataset.iataCode || this.extractIataCode(toInput.value),
          departureDate: dateInput.value,
          from: fromInput.value,
          to: toInput.value
        });
      }
    });

    return segmentData;
  }

  extractIataCode(inputValue) {
    if (!inputValue) return null;
    const match = inputValue.match(/\(([A-Z]{3})\)/);
    return match ? match[1] : null;
  }

  validateMultiCityData(segmentData) {
    const errors = [];

    segmentData.forEach((segment, index) => {
      if (!segment.originLocationCode) {
        errors.push(`Flight ${index + 1}: Please select departure airport`);
      }
      if (!segment.destinationLocationCode) {
        errors.push(`Flight ${index + 1}: Please select destination airport`);
      }
      if (!segment.departureDate) {
        errors.push(`Flight ${index + 1}: Please select departure date`);
      }
      
      // Check if departure date is in the past
      if (segment.departureDate) {
        const today = new Date().toISOString().split('T')[0];
        if (segment.departureDate < today) {
          errors.push(`Flight ${index + 1}: Departure date cannot be in the past`);
        }
      }

      // Check if same origin and destination
      if (segment.originLocationCode && segment.destinationLocationCode && 
          segment.originLocationCode === segment.destinationLocationCode) {
        errors.push(`Flight ${index + 1}: Origin and destination cannot be the same`);
      }
    });

    // Check date sequence (each flight should be after the previous one)
    for (let i = 1; i < segmentData.length; i++) {
      const prevDate = segmentData[i - 1].departureDate;
      const currentDate = segmentData[i].departureDate;
      
      if (prevDate && currentDate && currentDate <= prevDate) {
        errors.push(`Flight ${i + 1}: Date should be after Flight ${i} date`);
      }
    }

    return errors;
  }

  showValidationErrors(errors) {
    if (errors.length === 0) return;

    const errorMessage = errors.join('\n');
    alert('Please fix the following errors:\n\n' + errorMessage);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.multiCityManager = new MultiCityManager();
});

// Export for use in other scripts
window.MultiCityManager = MultiCityManager;
