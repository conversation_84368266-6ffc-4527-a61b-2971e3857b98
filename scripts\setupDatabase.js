const mysql = require("mysql2/promise");
const { initializeDatabase } = require("../config/database");
require("dotenv").config();

/**
 * Database Setup Script for Travel Hub BD
 * Creates database and initializes tables
 */

async function createDatabase() {
  const dialect = process.env.DB_DIALECT || "sqlite";

  if (dialect === "sqlite") {
    // For SQLite, just ensure directory exists
    const fs = require("fs");
    const path = require("path");
    const dbPath = process.env.DB_STORAGE || "./database/travel_hub_bd.sqlite";
    const dbDir = path.dirname(dbPath);

    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    console.log(`✅ SQLite database directory ready: ${dbDir}`);
    return;
  }

  // MySQL setup
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
  });

  try {
    // Create database if it doesn't exist
    const dbName = process.env.DB_NAME || "travel_hub_bd";
    await connection.execute(
      `CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`
    );
    console.log(`✅ Database '${dbName}' created or already exists`);

    await connection.end();
  } catch (error) {
    console.error("❌ Error creating database:", error.message);
    await connection.end();
    throw error;
  }
}

async function setupDatabase() {
  try {
    console.log("🗄️ Setting up Travel Hub BD Database...\n");

    // Step 1: Create database
    console.log("📝 Step 1: Creating database...");
    await createDatabase();

    // Step 2: Initialize tables and relationships
    console.log("📝 Step 2: Initializing tables...");
    await initializeDatabase();

    // Step 3: Create sample data (optional)
    console.log("📝 Step 3: Creating sample data...");
    await createSampleData();

    console.log("\n🎉 Database setup completed successfully!");
    console.log("\n📊 Database Structure:");
    console.log("   ├── users (User accounts)");
    console.log("   ├── user_preferences (User travel preferences)");
    console.log("   ├── flight_searches (Search history)");
    console.log("   ├── bookings (Flight bookings)");
    console.log("   ├── price_alerts (Price monitoring)");
    console.log("   └── search_history (Analytics data)");

    process.exit(0);
  } catch (error) {
    console.error("\n❌ Database setup failed:", error.message);
    console.error("\n💡 Troubleshooting:");
    console.error("   1. Make sure MySQL is running");
    console.error("   2. Check database credentials in .env file");
    console.error("   3. Ensure MySQL user has CREATE DATABASE privileges");
    process.exit(1);
  }
}

async function createSampleData() {
  const models = require("../models");

  try {
    // Create sample user
    const sampleUser = await models.User.findOrCreate({
      where: { email: "<EMAIL>" },
      defaults: {
        email: "<EMAIL>",
        password: "demo123456",
        first_name: "Demo",
        last_name: "User",
        phone: "+8801234567890",
        nationality: "BGD",
        preferred_currency: "BDT",
        is_verified: true,
      },
    });

    if (sampleUser[1]) {
      console.log("   ✅ Sample user created: <EMAIL>");

      // Create user preferences
      await models.UserPreference.create({
        user_id: sampleUser[0].id,
        preferred_class: "ECONOMY",
        preferred_airlines: ["BG", "EK", "QR"],
        currency_preference: "BDT",
        email_notifications: true,
        price_alert_notifications: true,
      });
      console.log("   ✅ Sample user preferences created");

      // Create sample search history
      await models.FlightSearch.create({
        user_id: sampleUser[0].id,
        search_type: "round-trip",
        origin_code: "DAC",
        origin_name: "Dhaka",
        destination_code: "DXB",
        destination_name: "Dubai",
        departure_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        return_date: new Date(Date.now() + 37 * 24 * 60 * 60 * 1000), // 37 days from now
        adults: 2,
        travel_class: "ECONOMY",
        currency_code: "BDT",
        search_results_count: 15,
        search_duration_ms: 2500,
      });
      console.log("   ✅ Sample search history created");

      // Create sample price alert
      await models.PriceAlert.create({
        user_id: sampleUser[0].id,
        origin_code: "DAC",
        destination_code: "LHR",
        target_price: 85000,
        currency_code: "BDT",
        travel_class: "ECONOMY",
        alert_frequency: "daily",
        notification_methods: ["email"],
        expires_at: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
      });
      console.log("   ✅ Sample price alert created");
    } else {
      console.log("   ℹ️ Sample user already exists");
    }

    // Create anonymous search history for analytics
    await models.SearchHistory.create({
      session_id: "sample_session_123",
      search_query: {
        originLocationCode: "DAC",
        destinationLocationCode: "KUL",
        departureDate: "2024-02-15",
        adults: 1,
        travelClass: "ECONOMY",
      },
      search_type: "flight",
      results_count: 12,
    });
    console.log("   ✅ Sample analytics data created");
  } catch (error) {
    console.error("   ⚠️ Error creating sample data:", error.message);
    // Don't throw error, sample data is optional
  }
}

// Run setup if called directly
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase, createDatabase, createSampleData };
