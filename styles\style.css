/* Additional Bootstrap-enhanced styles */
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
    url("../img/3.jpg");
  background-size: cover;
  background-position: center;
  color: white;
  padding: 120px 0;
  margin-top: -56px; /* Offset navbar height */
  height: 700px;
}

.feature-icon {
  font-size: 2.5rem;
  color: #0d6efd;
  margin-bottom: 1rem;
}

.testimonial-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #0d6efd;
}

.card {
  transition: transform 0.3s;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.nav-link {
  position: relative;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link-underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: white;
  transition: all 0.3s;
  transform: translateX(-50%);
}

.nav-link:hover .nav-link-underline {
  width: 70%;
}

.nav-link.active .nav-link-underline {
  width: 70%;
  background-color: #ffc107;
}

.navbar {
  padding: 24px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
/* navbar design  */
/* footer design  */

.footer {
  background-color: #2c3e50;
  color: white;
  padding-top: 3rem;
  padding-bottom: 1rem;
}

.footer a {
  color: #adb5bd;
  text-decoration: none;
  transition: color 0.2s;
}

.footer a:hover {
  color: white;
}

.logo-img {
  height: 40px;
  transition: transform 0.3s;
}

.logo-img:hover {
  transform: scale(1.05);
}

/* Custom CSS for enhanced features */
.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
  background-color: white !important;
}

.feature-card:hover .feature-icon {
  background-color: rgba(13, 110, 253, 0.2) !important;
  transform: scale(1.1);
}

.divider {
  height: 4px;
  width: 80px;
  background: linear-gradient(to right, #0d6efd, #6ea8fe);
  border-radius: 2px;
}
