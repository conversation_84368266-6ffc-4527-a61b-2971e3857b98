const models = require('../models');
require('dotenv').config();

async function addSampleData() {
  try {
    console.log('🗄️ Adding sample data to MySQL database...\n');

    // Create sample user
    const sampleUser = await models.User.findOrCreate({
      where: { email: '<EMAIL>' },
      defaults: {
        email: '<EMAIL>',
        password: 'demo123456',
        first_name: 'Demo',
        last_name: 'User',
        phone: '+8801234567890',
        nationality: 'BGD',
        preferred_currency: 'BDT',
        is_verified: true
      }
    });

    if (sampleUser[1]) {
      console.log('✅ Sample user created: <EMAIL>');
      
      // Create user preferences
      await models.UserPreference.create({
        user_id: sampleUser[0].id,
        preferred_class: 'ECONOMY',
        preferred_airlines: ['BG', 'EK', 'QR'],
        currency_preference: 'BDT',
        email_notifications: true,
        price_alert_notifications: true
      });
      console.log('✅ Sample user preferences created');

      // Create sample search history
      await models.FlightSearch.create({
        user_id: sampleUser[0].id,
        search_type: 'round-trip',
        origin_code: 'DAC',
        origin_name: 'Dhaka',
        destination_code: 'DXB',
        destination_name: 'Dubai',
        departure_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        return_date: new Date(Date.now() + 37 * 24 * 60 * 60 * 1000),
        adults: 2,
        travel_class: 'ECONOMY',
        currency_code: 'BDT',
        search_results_count: 15,
        search_duration_ms: 2500
      });
      console.log('✅ Sample search history created');

      // Create sample price alert
      await models.PriceAlert.create({
        user_id: sampleUser[0].id,
        origin_code: 'DAC',
        destination_code: 'LHR',
        target_price: 85000,
        currency_code: 'BDT',
        travel_class: 'ECONOMY',
        alert_frequency: 'daily',
        notification_methods: ['email'],
        expires_at: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
      });
      console.log('✅ Sample price alert created');

      // Create more sample searches
      await models.FlightSearch.create({
        user_id: sampleUser[0].id,
        search_type: 'one-way',
        origin_code: 'DAC',
        origin_name: 'Dhaka',
        destination_code: 'KUL',
        destination_name: 'Kuala Lumpur',
        departure_date: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
        adults: 1,
        travel_class: 'BUSINESS',
        currency_code: 'BDT',
        search_results_count: 8,
        search_duration_ms: 1800
      });
      console.log('✅ Additional search history created');

    } else {
      console.log('ℹ️ Sample user already exists');
    }

    // Create anonymous search history for analytics
    await models.SearchHistory.create({
      session_id: 'sample_session_123',
      search_query: {
        originLocationCode: 'DAC',
        destinationLocationCode: 'KUL',
        departureDate: '2024-02-15',
        adults: 1,
        travelClass: 'ECONOMY'
      },
      search_type: 'flight',
      results_count: 12
    });
    console.log('✅ Sample analytics data created');

    // Create more analytics data
    await models.SearchHistory.create({
      session_id: 'sample_session_456',
      search_query: {
        originLocationCode: 'DAC',
        destinationLocationCode: 'DXB',
        departureDate: '2024-03-10',
        adults: 2,
        travelClass: 'ECONOMY'
      },
      search_type: 'flight',
      results_count: 18
    });
    console.log('✅ Additional analytics data created');

    console.log('\n🎉 Sample data added successfully!');
    console.log('\n📊 You can now view the data in:');
    console.log('   🌐 phpMyAdmin: http://localhost/phpmyadmin');
    console.log('   🔍 Database Viewer: http://localhost:8080/pages/databaseViewer.html');
    console.log('   🧪 Database Test: http://localhost:8080/pages/databaseTest.html');

    process.exit(0);
  } catch (error) {
    console.error('\n❌ Failed to add sample data:', error.message);
    process.exit(1);
  }
}

addSampleData();
