/**
 * MCP-Style Integration for Enhanced Flight Features
 * Provides AI-like recommendations and advanced search capabilities
 */

class MCPIntegration {
  constructor() {
    this.baseURL = window.location.origin + "/api/mcp";
    this.defaultHeaders = {
      "Content-Type": "application/json",
    };
    this.init();
  }

  init() {
    this.setupAdvancedFeatures();

    // Add buttons immediately and also after a delay
    this.addMCPButtons();

    // Also add when page is fully loaded
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        setTimeout(() => this.addMCPButtons(), 500);
      });
    } else {
      setTimeout(() => this.addMCPButtons(), 500);
    }

    // Add floating button as fallback
    setTimeout(() => {
      if (!document.querySelector(".mcp-features")) {
        this.addFloatingMCPButton();
      }
    }, 2000);
  }

  setupAdvancedFeatures() {
    // Add MCP features to existing search
    if (window.flightSearchManager) {
      const originalHandleSearch =
        window.flightSearchManager.handleFlightSearch;
      window.flightSearchManager.handleFlightSearch = async function (form) {
        // Call original search
        await originalHandleSearch.call(this, form);

        // Add MCP enhancements
        if (window.mcpIntegration) {
          window.mcpIntegration.enhanceResults();
        }
      };
    }
  }

  addMCPButtons() {
    // Wait for DOM to be fully loaded
    setTimeout(() => {
      console.log("🤖 Adding MCP buttons...");

      // Try multiple selectors to find search forms
      const searchSelectors = [
        ".flight-search-form",
        "form[data-trip-type]",
        ".tab-pane form",
        "form",
      ];

      let formsFound = false;

      for (const selector of searchSelectors) {
        const forms = document.querySelectorAll(selector);
        console.log(`Found ${forms.length} forms with selector: ${selector}`);

        forms.forEach((form, index) => {
          // Look for button containers
          const buttonContainers = [
            form.querySelector(".text-center"),
            form.querySelector('[type="submit"]')?.parentElement,
            form.querySelector("button")?.parentElement,
            form.lastElementChild,
          ].filter(Boolean);

          buttonContainers.forEach((buttonContainer) => {
            if (
              buttonContainer &&
              !buttonContainer.querySelector(".mcp-features")
            ) {
              console.log("✅ Adding MCP buttons to form", index);

              const mcpButtons = document.createElement("div");
              mcpButtons.className =
                "mcp-features mt-4 p-3 bg-light rounded-3 border";
              mcpButtons.innerHTML = `
                <div class="text-center mb-3">
                  <h6 class="mb-2 text-primary">
                    <i class="fas fa-robot me-2"></i>AI Travel Assistant
                  </h6>
                  <small class="text-muted">Get personalized recommendations and insights</small>
                </div>
                <div class="row g-2">
                  <div class="col-md-3 col-6">
                    <button type="button" class="btn btn-outline-info btn-sm w-100" onclick="window.mcpIntegration.findBestDates()">
                      <i class="fas fa-calendar-check me-1"></i>Best Dates
                    </button>
                  </div>
                  <div class="col-md-3 col-6">
                    <button type="button" class="btn btn-outline-warning btn-sm w-100" onclick="window.mcpIntegration.predictPrices()">
                      <i class="fas fa-chart-line me-1"></i>Price Trends
                    </button>
                  </div>
                  <div class="col-md-3 col-6">
                    <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="window.mcpIntegration.suggestRoutes()">
                      <i class="fas fa-route me-1"></i>Route Ideas
                    </button>
                  </div>
                  <div class="col-md-3 col-6">
                    <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="window.mcpIntegration.advancedSearch()">
                      <i class="fas fa-magic me-1"></i>Smart Search
                    </button>
                  </div>
                </div>
              `;

              // Insert after the button container
              buttonContainer.insertAdjacentElement("afterend", mcpButtons);
              formsFound = true;
            }
          });
        });

        if (formsFound) break;
      }

      if (!formsFound) {
        console.log("⚠️ No suitable forms found, adding to body");
        this.addFloatingMCPButton();
      }
    }, 1000);
  }

  addFloatingMCPButton() {
    // Add a floating AI assistant button
    const floatingButton = document.createElement("div");
    floatingButton.className = "position-fixed";
    floatingButton.style.cssText = "bottom: 20px; right: 20px; z-index: 1000;";
    floatingButton.innerHTML = `
      <div class="dropdown dropup">
        <button class="btn btn-primary btn-lg rounded-circle shadow-lg" type="button" data-bs-toggle="dropdown">
          <i class="fas fa-robot"></i>
        </button>
        <div class="dropdown-menu dropdown-menu-end p-3" style="min-width: 250px;">
          <h6 class="dropdown-header">
            <i class="fas fa-robot me-2"></i>AI Travel Assistant
          </h6>
          <div class="d-grid gap-2">
            <button class="btn btn-outline-info btn-sm" onclick="window.mcpIntegration.findBestDates()">
              <i class="fas fa-calendar-check me-1"></i>Best Dates
            </button>
            <button class="btn btn-outline-warning btn-sm" onclick="window.mcpIntegration.predictPrices()">
              <i class="fas fa-chart-line me-1"></i>Price Trends
            </button>
            <button class="btn btn-outline-success btn-sm" onclick="window.mcpIntegration.suggestRoutes()">
              <i class="fas fa-route me-1"></i>Route Ideas
            </button>
            <button class="btn btn-outline-primary btn-sm" onclick="window.mcpIntegration.advancedSearch()">
              <i class="fas fa-magic me-1"></i>Smart Search
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(floatingButton);
    console.log("✅ Added floating MCP button");
  }

  async makeRequest(url, options = {}) {
    try {
      const response = await fetch(url, {
        headers: { ...this.defaultHeaders, ...options.headers },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || "Request failed");
      }

      return data;
    } catch (error) {
      console.error("MCP Request Error:", error);
      throw error;
    }
  }

  async advancedSearch() {
    try {
      this.showLoadingMessage("🤖 Running AI-powered search...");

      const formData = this.getActiveFormData();
      if (!formData) {
        this.showErrorMessage("Please fill in search details first");
        return;
      }

      const searchParams = {
        ...formData,
        includeAnalysis: true,
        maxPrice: this.getMaxPricePreference(),
        preferredAirlines: this.getPreferredAirlines(),
      };

      const response = await this.makeRequest(
        `${this.baseURL}/search-flights-advanced`,
        {
          method: "POST",
          body: JSON.stringify(searchParams),
        }
      );

      this.displayAdvancedResults(response.data);
      this.showSuccessMessage("✨ AI recommendations ready!");
    } catch (error) {
      console.error("Advanced search error:", error);
      this.showErrorMessage(
        "Failed to get AI recommendations: " + error.message
      );
    }
  }

  async findBestDates() {
    try {
      this.showLoadingMessage("📅 Finding best travel dates...");

      const formData = this.getActiveFormData();
      if (
        !formData ||
        !formData.originLocationCode ||
        !formData.destinationLocationCode
      ) {
        this.showErrorMessage(
          "Please select departure and destination airports"
        );
        return;
      }

      const searchParams = {
        origin: formData.originLocationCode,
        destination: formData.destinationLocationCode,
        departureMonth: formData.departureDate
          ? formData.departureDate.substring(0, 7)
          : this.getNextMonth(),
        budget: this.getBudgetPreference(),
      };

      const response = await this.makeRequest(
        `${this.baseURL}/find-best-dates`,
        {
          method: "POST",
          body: JSON.stringify(searchParams),
        }
      );

      this.displayBestDates(response.data);
      this.showSuccessMessage("📅 Best travel dates found!");
    } catch (error) {
      console.error("Best dates error:", error);
      this.showErrorMessage("Failed to find best dates: " + error.message);
    }
  }

  async predictPrices() {
    try {
      this.showLoadingMessage("🔮 Analyzing price trends...");

      const formData = this.getActiveFormData();
      if (
        !formData ||
        !formData.originLocationCode ||
        !formData.destinationLocationCode
      ) {
        this.showErrorMessage(
          "Please select departure and destination airports"
        );
        return;
      }

      const searchParams = {
        originLocationCode: formData.originLocationCode,
        destinationLocationCode: formData.destinationLocationCode,
        departureDate: formData.departureDate || this.getDefaultDate(),
      };

      const response = await this.makeRequest(
        `${this.baseURL}/predict-prices`,
        {
          method: "POST",
          body: JSON.stringify(searchParams),
        }
      );

      this.displayPricePrediction(response.data);
      this.showSuccessMessage("📊 Price analysis complete!");
    } catch (error) {
      console.error("Price prediction error:", error);
      this.showErrorMessage("Failed to predict prices: " + error.message);
    }
  }

  async suggestRoutes() {
    try {
      this.showLoadingMessage("🗺️ Finding amazing destinations...");

      const formData = this.getActiveFormData();
      if (!formData || !formData.originLocationCode) {
        this.showErrorMessage("Please select departure airport");
        return;
      }

      const searchParams = {
        origin: formData.originLocationCode,
        budget: this.getBudgetPreference() || 100000,
        interests: this.getInterestPreferences(),
        duration: "5-10",
      };

      const response = await this.makeRequest(
        `${this.baseURL}/suggest-routes`,
        {
          method: "POST",
          body: JSON.stringify(searchParams),
        }
      );

      this.displayRouteSuggestions(response.data);
      this.showSuccessMessage("✈️ Amazing destinations found!");
    } catch (error) {
      console.error("Route suggestions error:", error);
      this.showErrorMessage(
        "Failed to get route suggestions: " + error.message
      );
    }
  }

  getActiveFormData() {
    // Get data from currently active tab
    const activeTab = document.querySelector(
      ".tab-pane.active .flight-search-form"
    );
    if (!activeTab) return null;

    const tripType = activeTab.dataset.tripType || "round-trip";

    if (tripType === "round-trip") {
      return {
        tripType: "round-trip",
        originLocationCode: this.extractIataCode(
          document.getElementById("from-city")?.value
        ),
        destinationLocationCode: this.extractIataCode(
          document.getElementById("to-city")?.value
        ),
        departureDate: document.getElementById("departure-date")?.value,
        returnDate: document.getElementById("return-date")?.value,
        adults: parseInt(document.getElementById("passengers")?.value) || 1,
        travelClass: document.getElementById("class")?.value || "ECONOMY",
      };
    } else if (tripType === "one-way") {
      return {
        tripType: "one-way",
        originLocationCode: this.extractIataCode(
          document.getElementById("from-city-ow")?.value
        ),
        destinationLocationCode: this.extractIataCode(
          document.getElementById("to-city-ow")?.value
        ),
        departureDate: document.getElementById("departure-date-ow")?.value,
        adults: parseInt(document.getElementById("passengers-ow")?.value) || 1,
        travelClass: document.getElementById("class-ow")?.value || "ECONOMY",
      };
    }

    return null;
  }

  extractIataCode(inputValue) {
    if (!inputValue) return null;
    const match = inputValue.match(/\(([A-Z]{3})\)/);
    return match ? match[1] : null;
  }

  getMaxPricePreference() {
    // Could be from user preferences or form input
    return 150000; // Default max price in BDT
  }

  getPreferredAirlines() {
    // Could be from user preferences
    return ["BG", "EK", "QR", "TK"]; // Biman, Emirates, Qatar, Turkish
  }

  getBudgetPreference() {
    // Could be from user input or preferences
    return 80000; // Default budget in BDT
  }

  getInterestPreferences() {
    // Could be from user profile
    return ["beach", "culture", "food", "adventure"];
  }

  getNextMonth() {
    const date = new Date();
    date.setMonth(date.getMonth() + 1);
    return date.toISOString().substring(0, 7);
  }

  getDefaultDate() {
    const date = new Date();
    date.setDate(date.getDate() + 30);
    return date.toISOString().split("T")[0];
  }

  displayAdvancedResults(data) {
    this.createMCPModal(
      "AI Flight Recommendations",
      this.generateAdvancedResultsHTML(data)
    );
  }

  displayBestDates(data) {
    this.createMCPModal("Best Travel Dates", this.generateBestDatesHTML(data));
  }

  displayPricePrediction(data) {
    this.createMCPModal(
      "Price Analysis & Prediction",
      this.generatePricePredictionHTML(data)
    );
  }

  displayRouteSuggestions(data) {
    this.createMCPModal(
      "Route Suggestions",
      this.generateRouteSuggestionsHTML(data)
    );
  }

  createMCPModal(title, content) {
    // Remove existing modal
    const existingModal = document.getElementById("mcpModal");
    if (existingModal) {
      existingModal.remove();
    }

    // Create new modal
    const modalHTML = `
      <div class="modal fade" id="mcpModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header bg-primary text-white">
              <h5 class="modal-title">
                <i class="fas fa-robot me-2"></i>${title}
              </h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              ${content}
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML("beforeend", modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById("mcpModal"));
    modal.show();
  }

  generateAdvancedResultsHTML(data) {
    return `
      <div class="mcp-results">
        <div class="alert alert-info">
          <i class="fas fa-magic me-2"></i>
          <strong>AI Analysis Complete!</strong> Found ${
            data.flights.length
          } flights with smart recommendations.
        </div>
        
        ${
          data.recommendations
            ? `
          <h6 class="fw-bold mb-3">🎯 Smart Recommendations</h6>
          <div class="row g-3 mb-4">
            ${data.recommendations
              .map(
                (rec) => `
              <div class="col-md-4">
                <div class="card border-${this.getRecommendationColor(
                  rec.type
                )}">
                  <div class="card-body">
                    <h6 class="card-title text-${this.getRecommendationColor(
                      rec.type
                    )}">
                      ${this.getRecommendationIcon(
                        rec.type
                      )} ${rec.type.replace("_", " ")}
                    </h6>
                    <p class="card-text small">${rec.reason}</p>
                    <div class="fw-bold">৳ ${parseFloat(
                      rec.flight.price.total
                    ).toLocaleString()}</div>
                  </div>
                </div>
              </div>
            `
              )
              .join("")}
          </div>
        `
            : ""
        }

        ${
          data.analysis
            ? `
          <h6 class="fw-bold mb-3">📊 Price Analysis</h6>
          <div class="alert alert-light">
            <div class="row">
              <div class="col-md-6">
                <strong>Price Level:</strong> ${this.formatPriceLevel(
                  data.analysis
                )}
              </div>
              <div class="col-md-6">
                <strong>Recommendation:</strong> ${this.formatPriceRecommendation(
                  data.analysis
                )}
              </div>
            </div>
          </div>
        `
            : ""
        }
      </div>
    `;
  }

  generateBestDatesHTML(data) {
    return `
      <div class="best-dates-results">
        <div class="alert alert-success">
          <i class="fas fa-calendar-check me-2"></i>
          <strong>Best Travel Dates Found!</strong> Save up to ৳ ${
            data.insights?.bestSavings || "N/A"
          } by choosing the right dates.
        </div>
        
        <div class="row g-3">
          ${data.bestDates
            .slice(0, 6)
            .map(
              (date, index) => `
            <div class="col-md-6">
              <div class="card ${index === 0 ? "border-success" : ""}">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 class="card-title mb-1">${this.formatDate(
                        date.departureDate
                      )}</h6>
                      <small class="text-muted">${date.recommendation}</small>
                    </div>
                    <div class="text-end">
                      <div class="fw-bold text-success">৳ ${parseFloat(
                        date.price.total
                      ).toLocaleString()}</div>
                      ${
                        date.savings
                          ? `<small class="text-success">Save ৳ ${date.savings}</small>`
                          : ""
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>
          `
            )
            .join("")}
        </div>
      </div>
    `;
  }

  generatePricePredictionHTML(data) {
    return `
      <div class="price-prediction-results">
        <div class="alert alert-info">
          <i class="fas fa-chart-line me-2"></i>
          <strong>Price Analysis:</strong> ${
            data.prediction?.recommendation || "Analysis complete"
          }
        </div>
        
        <div class="row g-4">
          <div class="col-md-6">
            <h6 class="fw-bold">Current Price Level</h6>
            <div class="card">
              <div class="card-body text-center">
                <div class="display-6 mb-2">${this.getPriceLevelEmoji(
                  data.prediction?.currentPriceLevel
                )}</div>
                <h5 class="text-${this.getPriceLevelColor(
                  data.prediction?.currentPriceLevel
                )}">
                  ${data.prediction?.currentPriceLevel || "Unknown"}
                </h5>
                <p class="small text-muted">${
                  data.prediction?.insights?.bestTimeToBook ||
                  "Monitor prices regularly"
                }</p>
              </div>
            </div>
          </div>
          
          <div class="col-md-6">
            <h6 class="fw-bold">Price Range</h6>
            <div class="card">
              <div class="card-body">
                <div class="mb-2">
                  <small class="text-muted">Lowest 25%</small>
                  <div class="fw-bold text-success">৳ ${
                    data.prediction?.insights?.priceRange?.min || "N/A"
                  }</div>
                </div>
                <div class="mb-2">
                  <small class="text-muted">Average</small>
                  <div class="fw-bold">৳ ${
                    data.prediction?.insights?.priceRange?.median || "N/A"
                  }</div>
                </div>
                <div>
                  <small class="text-muted">Highest 25%</small>
                  <div class="fw-bold text-danger">৳ ${
                    data.prediction?.insights?.priceRange?.max || "N/A"
                  }</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  generateRouteSuggestionsHTML(data) {
    return `
      <div class="route-suggestions-results">
        <div class="alert alert-primary">
          <i class="fas fa-map-marked-alt me-2"></i>
          <strong>Amazing Destinations!</strong> Found ${
            data.suggestions.length
          } great places within your budget.
        </div>
        
        <div class="row g-3">
          ${data.suggestions
            .slice(0, 8)
            .map(
              (dest) => `
            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="card-title mb-0">
                      ${dest.destinationName || dest.destination}
                      ${
                        dest.country
                          ? `<small class="text-muted">, ${dest.country}</small>`
                          : ""
                      }
                    </h6>
                    <span class="badge bg-primary">৳ ${parseFloat(
                      dest.price.total
                    ).toLocaleString()}</span>
                  </div>
                  <p class="card-text small text-muted">
                    ${
                      dest.highlights?.join(", ") ||
                      dest.recommendations?.highlights?.join(", ") ||
                      "Great destination for travelers"
                    }
                  </p>
                  <div class="small">
                    <i class="fas fa-calendar me-1"></i>
                    Best time: ${dest.recommendations?.bestTime || "Year round"}
                  </div>
                  <div class="small mt-1">
                    <i class="fas fa-plane me-1"></i>
                    ${dest.origin} → ${dest.destination}
                  </div>
                </div>
              </div>
            </div>
          `
            )
            .join("")}
        </div>
      </div>
    `;
  }

  // Helper methods for formatting
  getRecommendationColor(type) {
    const colors = {
      BEST_VALUE: "primary",
      FASTEST: "success",
      CHEAPEST: "warning",
    };
    return colors[type] || "secondary";
  }

  getRecommendationIcon(type) {
    const icons = {
      BEST_VALUE: "🎯",
      FASTEST: "⚡",
      CHEAPEST: "💰",
    };
    return icons[type] || "✈️";
  }

  getPriceLevelEmoji(level) {
    const emojis = {
      LOW: "🟢",
      BELOW_AVERAGE: "🟡",
      ABOVE_AVERAGE: "🟠",
      HIGH: "🔴",
    };
    return emojis[level] || "⚪";
  }

  getPriceLevelColor(level) {
    const colors = {
      LOW: "success",
      BELOW_AVERAGE: "warning",
      ABOVE_AVERAGE: "warning",
      HIGH: "danger",
    };
    return colors[level] || "secondary";
  }

  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  }

  formatPriceLevel(analysis) {
    return analysis[0]?.quartileRanking || "Unknown";
  }

  formatPriceRecommendation(analysis) {
    const quartile = analysis[0]?.quartileRanking;
    if (quartile === "FIRST") return "Excellent time to book!";
    if (quartile === "SECOND") return "Good time to book";
    if (quartile === "THIRD") return "Consider waiting";
    if (quartile === "FOURTH") return "Wait for better prices";
    return "Monitor prices";
  }

  enhanceResults() {
    // Add MCP insights to existing results
    const resultsContainer = document.querySelector(
      "#flight-results-container"
    );
    if (resultsContainer && resultsContainer.style.display !== "none") {
      this.addMCPInsightsToResults();
    }
  }

  addMCPInsightsToResults() {
    // Add AI insights banner to results
    const container = document.querySelector(
      "#flight-results-container .container"
    );
    if (container && !container.querySelector(".mcp-insights")) {
      const insightsHTML = `
        <div class="mcp-insights alert alert-info mt-4">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h6 class="mb-1"><i class="fas fa-robot me-2"></i>AI Travel Assistant</h6>
              <small>Get personalized recommendations, price predictions, and travel insights</small>
            </div>
            <div class="col-md-4 text-end">
              <button class="btn btn-outline-primary btn-sm" onclick="mcpIntegration.advancedSearch()">
                <i class="fas fa-magic me-1"></i>Get AI Insights
              </button>
            </div>
          </div>
        </div>
      `;
      container.insertAdjacentHTML("beforeend", insightsHTML);
    }
  }

  showLoadingMessage(message) {
    this.showMessage(message, "info", 0); // 0 = don't auto-hide
  }

  showSuccessMessage(message) {
    this.showMessage(message, "success");
  }

  showErrorMessage(message) {
    this.showMessage(message, "error");
  }

  showMessage(message, type = "info", autoHide = 5000) {
    // Create or update message element
    let messageEl = document.getElementById("mcp-message");

    if (!messageEl) {
      messageEl = document.createElement("div");
      messageEl.id = "mcp-message";
      messageEl.className = "alert alert-dismissible fade show position-fixed";
      messageEl.style.cssText =
        "top: 100px; right: 20px; z-index: 9999; min-width: 300px;";
      document.body.appendChild(messageEl);
    }

    const alertClass =
      type === "error"
        ? "alert-danger"
        : type === "success"
        ? "alert-success"
        : "alert-info";

    messageEl.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    messageEl.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Auto hide if specified
    if (autoHide > 0) {
      setTimeout(() => {
        if (messageEl && messageEl.parentElement) {
          messageEl.remove();
        }
      }, autoHide);
    }
  }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.mcpIntegration = new MCPIntegration();
});

// Export for use in other scripts
window.MCPIntegration = MCPIntegration;
