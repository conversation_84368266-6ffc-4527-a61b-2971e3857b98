.contact-card {
  transition: transform 0.3s, box-shadow 0.3s;
}
.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.contact-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #0d6efd;
}
.map-container {
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
}
.form-control,
.form-select {
  padding: 12px;
  border-radius: 8px;
}
/* নতুন CSS যোগ করুন */
.page-header {
  background: linear-gradient(hsla(218, 13%, 24%, 0.8), rgba(58, 64, 72, 0.9)),
    url("https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 100px 0;
  color: white;
  text-align: center;
  position: relative;
}

/* মোবাইল ডিভাইসের জন্য রেস্পন্সিভ স্টাইল */
@media (max-width: 768px) {
  .page-header {
    padding: 70px 0;
    background-position: 65% center;
  }
}
