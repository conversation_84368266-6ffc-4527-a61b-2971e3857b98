module.exports = (sequelize, DataTypes) => {
  const Contact = sequelize.define('Contact', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
        notEmpty: true
      }
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true,
        notEmpty: true
      }
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [10, 20]
      }
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [5, 200],
        notEmpty: true
      }
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        len: [10, 2000],
        notEmpty: true
      }
    },
    contact_type: {
      type: DataTypes.ENUM(
        'general_inquiry',
        'booking_support',
        'technical_issue',
        'complaint',
        'suggestion',
        'partnership',
        'media_inquiry',
        'other'
      ),
      allowNull: false,
      defaultValue: 'general_inquiry'
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
      allowNull: false,
      defaultValue: 'medium'
    },
    status: {
      type: DataTypes.ENUM('new', 'in_progress', 'resolved', 'closed'),
      allowNull: false,
      defaultValue: 'new'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'If user is logged in'
    },
    session_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'For anonymous users'
    },
    ip_address: {
      type: DataTypes.STRING,
      allowNull: true
    },
    user_agent: {
      type: DataTypes.STRING,
      allowNull: true
    },
    referrer: {
      type: DataTypes.STRING,
      allowNull: true
    },
    assigned_to: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Staff member assigned to handle this contact'
    },
    response_sent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    response_sent_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    resolved_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    admin_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Internal notes for admin'
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5
      },
      comment: 'Customer satisfaction rating (1-5)'
    },
    follow_up_required: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    follow_up_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'contacts',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['email']
      },
      {
        fields: ['status']
      },
      {
        fields: ['contact_type']
      },
      {
        fields: ['priority']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['assigned_to']
      }
    ]
  });

  // Instance methods
  Contact.prototype.isUrgent = function() {
    return this.priority === 'urgent' || this.priority === 'high';
  };

  Contact.prototype.isResolved = function() {
    return this.status === 'resolved' || this.status === 'closed';
  };

  Contact.prototype.getResponseTime = function() {
    if (!this.response_sent_at) return null;
    
    const created = new Date(this.created_at);
    const responded = new Date(this.response_sent_at);
    const diffHours = (responded - created) / (1000 * 60 * 60);
    
    return Math.round(diffHours * 100) / 100; // Round to 2 decimal places
  };

  Contact.prototype.getResolutionTime = function() {
    if (!this.resolved_at) return null;
    
    const created = new Date(this.created_at);
    const resolved = new Date(this.resolved_at);
    const diffHours = (resolved - created) / (1000 * 60 * 60);
    
    return Math.round(diffHours * 100) / 100;
  };

  // Class methods
  Contact.findByStatus = function(status, limit = 50) {
    return this.findAll({
      where: { status },
      order: [['created_at', 'DESC']],
      limit,
      include: [
        {
          model: sequelize.models.User,
          as: 'user',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false
        }
      ]
    });
  };

  Contact.findByPriority = function(priority, limit = 50) {
    return this.findAll({
      where: { priority },
      order: [['created_at', 'DESC']],
      limit
    });
  };

  Contact.findPending = function(limit = 50) {
    return this.findAll({
      where: {
        status: ['new', 'in_progress']
      },
      order: [['priority', 'DESC'], ['created_at', 'ASC']],
      limit
    });
  };

  Contact.getStats = function(days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return this.findAll({
      attributes: [
        'status',
        'contact_type',
        'priority',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('AVG', sequelize.col('rating')), 'avg_rating']
      ],
      where: {
        created_at: {
          [sequelize.Op.gte]: startDate
        }
      },
      group: ['status', 'contact_type', 'priority'],
      raw: true
    });
  };

  return Contact;
};
