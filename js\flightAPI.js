/**
 * Flight API Client for TravelHub BD
 * Handles all API communications with the backend
 */

class FlightAPI {
  constructor() {
    this.baseURL = window.location.origin + "/api";
    this.defaultHeaders = {
      "Content-Type": "application/json",
    };

    // Log API base URL for debugging
    console.log("🔗 API Base URL:", this.baseURL);
  }

  /**
   * Make HTTP request with error handling
   */
  async makeRequest(url, options = {}) {
    try {
      const response = await fetch(url, {
        headers: { ...this.defaultHeaders, ...options.headers },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.message || data.error || "Request failed";
        const error = new Error(errorMessage);
        error.status = response.status;
        error.code = data.code;
        error.retryAfter = data.retryAfter;
        throw error;
      }

      return data;
    } catch (error) {
      console.error("API Request Error:", error);

      // Handle network errors
      if (error.name === "TypeError" && error.message.includes("fetch")) {
        error.message = "Network error. Please check your connection.";
      }

      throw error;
    }
  }

  /**
   * Test API connectivity
   */
  async testConnection() {
    const url = `${this.baseURL}/test`;
    return await this.makeRequest(url);
  }

  /**
   * Search airports by keyword
   */
  async searchAirports(keyword) {
    if (!keyword || keyword.length < 2) {
      return { success: true, data: [] };
    }

    const url = `${this.baseURL}/airports/search?keyword=${encodeURIComponent(
      keyword
    )}`;
    return await this.makeRequest(url);
  }

  /**
   * Get airport details by IATA code
   */
  async getAirportDetails(iataCode) {
    const url = `${this.baseURL}/airports/${iataCode}`;
    return await this.makeRequest(url);
  }

  /**
   * Search flights
   */
  async searchFlights(searchParams) {
    const url = `${this.baseURL}/flights/search`;
    return await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(searchParams),
    });
  }

  /**
   * Get flight details
   */
  async getFlightDetails(offerId) {
    const url = `${this.baseURL}/flights/details/${offerId}`;
    return await this.makeRequest(url);
  }

  /**
   * Get flight price analysis
   */
  async getFlightPriceAnalysis(analysisParams) {
    const url = `${this.baseURL}/flights/price-analysis`;
    return await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(analysisParams),
    });
  }

  /**
   * Find cheapest dates
   */
  async findCheapestDates(searchParams) {
    const url = `${this.baseURL}/flights/cheapest-dates`;
    return await this.makeRequest(url, {
      method: "POST",
      body: JSON.stringify(searchParams),
    });
  }
}

/**
 * Flight Search Manager
 * Handles flight search functionality and UI updates
 */
class FlightSearchManager {
  constructor() {
    this.api = new FlightAPI();
    this.searchResults = [];
    this.isLoading = false;
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.setupAirportAutocomplete();
    this.testAPIConnection();
  }

  async testAPIConnection() {
    try {
      const response = await this.api.testConnection();
      console.log("✅ API Connection successful:", response);
    } catch (error) {
      console.error("❌ API Connection failed:", error);
      this.showErrorMessage(
        "API connection failed. Please check if the server is running."
      );
    }
  }

  setupEventListeners() {
    // Flight search form submission for all tabs
    const searchForms = document.querySelectorAll(".flight-search-form");
    searchForms.forEach((form) => {
      form.addEventListener("submit", (e) => {
        e.preventDefault();
        this.handleFlightSearch(form);
      });
    });

    // Tab switching
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach((button) => {
      button.addEventListener("click", () => {
        this.handleTabSwitch(button);
      });
    });
  }

  setupAirportAutocomplete() {
    // Get all airport input fields from all tabs
    const airportInputs = document.querySelectorAll(
      '#from-city, #to-city, #from-city-ow, #to-city-ow, .multi-city-segment input[type="text"]'
    );

    airportInputs.forEach((input) => {
      let debounceTimer;

      input.addEventListener("input", (e) => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
          this.handleAirportSearch(e.target);
        }, 300);
      });

      input.addEventListener("focus", () => {
        this.showAirportDropdown(input);
      });

      input.addEventListener("blur", () => {
        // Delay hiding to allow selection
        setTimeout(() => {
          this.hideAirportDropdown(input);
        }, 200);
      });
    });
  }

  async handleAirportSearch(input) {
    const keyword = input.value.trim();

    if (keyword.length < 2) {
      this.hideAirportDropdown(input);
      return;
    }

    try {
      const response = await this.api.searchAirports(keyword);
      this.displayAirportSuggestions(input, response.data);
    } catch (error) {
      console.error("Airport search error:", error);
      this.hideAirportDropdown(input);
    }
  }

  displayAirportSuggestions(input, airports) {
    let dropdown = input.parentElement.querySelector(".airport-dropdown");

    if (!dropdown) {
      dropdown = document.createElement("div");
      dropdown.className =
        "airport-dropdown position-absolute bg-white border rounded shadow-lg w-100";
      dropdown.style.cssText =
        "top: 100%; left: 0; z-index: 1000; max-height: 300px; overflow-y: auto;";
      input.parentElement.style.position = "relative";
      input.parentElement.appendChild(dropdown);
    }

    if (airports.length === 0) {
      dropdown.innerHTML =
        '<div class="p-3 text-muted">No airports found</div>';
      return;
    }

    dropdown.innerHTML = airports
      .map(
        (airport) => `
      <div class="airport-option p-3 border-bottom cursor-pointer hover:bg-light" 
           data-iata="${airport.iataCode}" 
           data-name="${airport.name}"
           data-city="${airport.address?.cityName || ""}"
           style="cursor: pointer;">
        <div class="fw-bold">${airport.name}</div>
        <div class="small text-muted">
          ${airport.iataCode} - ${airport.address?.cityName || ""}, ${
          airport.address?.countryName || ""
        }
        </div>
      </div>
    `
      )
      .join("");

    // Add click handlers
    dropdown.querySelectorAll(".airport-option").forEach((option) => {
      option.addEventListener("click", () => {
        const iataCode = option.dataset.iata;
        const name = option.dataset.name;
        const city = option.dataset.city;

        input.value = `${city || name} (${iataCode})`;
        input.dataset.iataCode = iataCode;
        this.hideAirportDropdown(input);
      });

      option.addEventListener("mouseenter", () => {
        option.style.backgroundColor = "#f8f9fa";
      });

      option.addEventListener("mouseleave", () => {
        option.style.backgroundColor = "";
      });
    });

    dropdown.style.display = "block";
  }

  showAirportDropdown(input) {
    const dropdown = input.parentElement.querySelector(".airport-dropdown");
    if (dropdown) {
      dropdown.style.display = "block";
    }
  }

  hideAirportDropdown(input) {
    const dropdown = input.parentElement.querySelector(".airport-dropdown");
    if (dropdown) {
      dropdown.style.display = "none";
    }
  }

  async handleFlightSearch(form) {
    if (this.isLoading) return;

    const formData = this.getFormData(form);

    if (!this.validateFormData(formData)) {
      return;
    }

    this.setLoadingState(true);
    this.showSearchProgress();

    try {
      const response = await this.api.searchFlights(formData);
      this.searchResults = response.data;
      this.displaySearchResults(response.data, formData);
      this.showSuccessMessage(`Found ${response.data.length} flights`);
    } catch (error) {
      console.error("Flight search error:", error);
      this.showErrorMessage(error.message || "Failed to search flights");
    } finally {
      this.setLoadingState(false);
      this.hideSearchProgress();
    }
  }

  getFormData(form) {
    const tripType = form?.dataset.tripType || "round-trip";

    if (tripType === "round-trip") {
      const fromInput = document.getElementById("from-city");
      const toInput = document.getElementById("to-city");

      return {
        tripType: "round-trip",
        originLocationCode:
          fromInput?.dataset.iataCode || this.extractIataCode(fromInput?.value),
        destinationLocationCode:
          toInput?.dataset.iataCode || this.extractIataCode(toInput?.value),
        departureDate: document.getElementById("departure-date")?.value,
        returnDate: document.getElementById("return-date")?.value,
        adults: parseInt(document.getElementById("passengers")?.value) || 1,
        travelClass: document.getElementById("class")?.value || "ECONOMY",
        currencyCode: "BDT",
        max: 10,
      };
    } else if (tripType === "one-way") {
      const fromInput = document.getElementById("from-city-ow");
      const toInput = document.getElementById("to-city-ow");

      return {
        tripType: "one-way",
        originLocationCode:
          fromInput?.dataset.iataCode || this.extractIataCode(fromInput?.value),
        destinationLocationCode:
          toInput?.dataset.iataCode || this.extractIataCode(toInput?.value),
        departureDate: document.getElementById("departure-date-ow")?.value,
        adults: parseInt(document.getElementById("passengers-ow")?.value) || 1,
        travelClass: document.getElementById("class-ow")?.value || "ECONOMY",
        currencyCode: "BDT",
        max: 10,
      };
    } else if (tripType === "multi-city") {
      // For now, return first segment data (multi-city search needs special handling)
      const segments = document.querySelectorAll(".multi-city-segment");
      const firstSegment = segments[0];
      const fromInput = firstSegment?.querySelector(
        'input[placeholder*="From"]'
      );
      const toInput = firstSegment?.querySelector('input[placeholder*="To"]');
      const dateInput = firstSegment?.querySelector('input[type="date"]');

      return {
        tripType: "multi-city",
        originLocationCode:
          fromInput?.dataset.iataCode || this.extractIataCode(fromInput?.value),
        destinationLocationCode:
          toInput?.dataset.iataCode || this.extractIataCode(toInput?.value),
        departureDate: dateInput?.value,
        adults: parseInt(document.getElementById("passengers-mc")?.value) || 1,
        travelClass: document.getElementById("class-mc")?.value || "ECONOMY",
        currencyCode: "BDT",
        max: 10,
      };
    }

    // Fallback to round-trip
    return this.getFormData(
      document.querySelector('[data-trip-type="round-trip"]')
    );
  }

  extractIataCode(inputValue) {
    if (!inputValue) return null;
    const match = inputValue.match(/\(([A-Z]{3})\)/);
    return match ? match[1] : null;
  }

  validateFormData(formData) {
    if (!formData.originLocationCode) {
      this.showErrorMessage("Please select a departure airport");
      return false;
    }

    if (!formData.destinationLocationCode) {
      this.showErrorMessage("Please select a destination airport");
      return false;
    }

    if (!formData.departureDate) {
      this.showErrorMessage("Please select a departure date");
      return false;
    }

    const today = new Date().toISOString().split("T")[0];
    if (formData.departureDate < today) {
      this.showErrorMessage("Departure date cannot be in the past");
      return false;
    }

    return true;
  }

  setLoadingState(loading) {
    this.isLoading = loading;
    const searchButton = document.querySelector(
      '.flight-search-form button[type="submit"]'
    );

    if (searchButton) {
      if (loading) {
        searchButton.disabled = true;
        searchButton.innerHTML =
          '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
      } else {
        searchButton.disabled = false;
        searchButton.innerHTML =
          '<i class="fas fa-search me-2"></i><span class="fw-bold">Search Flights</span>';
      }
    }
  }

  showSearchProgress() {
    // Add progress indicator if needed
    console.log("🔍 Searching for flights...");
  }

  hideSearchProgress() {
    // Hide progress indicator
    console.log("✅ Search completed");
  }

  showSuccessMessage(message) {
    this.showMessage(message, "success");
  }

  showErrorMessage(message) {
    this.showMessage(message, "error");
  }

  showMessage(message, type = "info") {
    // Create or update message element
    let messageEl = document.getElementById("flight-search-message");

    if (!messageEl) {
      messageEl = document.createElement("div");
      messageEl.id = "flight-search-message";
      messageEl.className = "alert alert-dismissible fade show position-fixed";
      messageEl.style.cssText =
        "top: 100px; right: 20px; z-index: 9999; min-width: 300px;";
      document.body.appendChild(messageEl);
    }

    const alertClass =
      type === "error"
        ? "alert-danger"
        : type === "success"
        ? "alert-success"
        : "alert-info";

    messageEl.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    messageEl.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Auto hide after 5 seconds
    setTimeout(() => {
      if (messageEl && messageEl.parentElement) {
        messageEl.remove();
      }
    }, 5000);
  }

  handleTabSwitch(button) {
    // Handle different flight types (round-trip, one-way, multi-city)
    const target = button.getAttribute("data-bs-target");
    console.log("Tab switched to:", target);
  }

  displaySearchResults(flights, searchParams) {
    // Use FlightResultsManager if available
    if (window.flightResultsManager) {
      window.flightResultsManager.displayResults(flights, searchParams);
    } else {
      console.log("Displaying flight results:", flights);

      // Fallback: scroll to results section
      const resultsSection = document.querySelector(".py-5.bg-light");
      if (resultsSection) {
        resultsSection.scrollIntoView({ behavior: "smooth" });
      }
    }
  }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.flightSearchManager = new FlightSearchManager();
});

// Export for use in other scripts
window.FlightAPI = FlightAPI;
window.FlightSearchManager = FlightSearchManager;
