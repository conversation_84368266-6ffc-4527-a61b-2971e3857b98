:root {
  --primary-color: #0d6efd;
  --secondary-color: #0b5ed7;
  --accent-color: #ffc107;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.booking-hero {
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
    url("https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80");
  background-size: cover;
  background-position: center;
  color: white;
  padding: 100px 0;
  margin-bottom: 50px;
}

.booking-card {
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s;
  margin-bottom: 30px;
}

.booking-card:hover {
  transform: translateY(-5px);
}

.booking-card-header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 15px 20px;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.flight-type-tabs .nav-link {
  color: #495057;
  font-weight: 500;
}

.flight-type-tabs .nav-link.active {
  color: var(--primary-color);
  border-bottom: 3px solid var(--primary-color);
  background-color: transparent;
}

.search-btn {
  padding: 12px 30px;
  font-weight: 600;
  letter-spacing: 0.5px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border: none;
}

.ticket-card {
  border-left: 4px solid var(--primary-color);
  transition: all 0.3s;
}

.ticket-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.price-badge {
  background-color: var(--accent-color);
  color: #212529;
  font-weight: bold;
}

.flight-icon {
  color: var(--primary-color);
  font-size: 1.5rem;
}

.time-display {
  font-size: 1.2rem;
  font-weight: 600;
}

.airline-logo {
  max-height: 40px;
  max-width: 100px;
}

.nav-link-underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: white;
  transition: all 0.3s;
  transform: translateX(-50%);
}

.nav-link:hover .nav-link-underline {
  width: 70%;
}

.nav-link.active .nav-link-underline {
  width: 70%;
  background-color: var(--accent-color);
}
.footer {
  background-color: #2c3e50;
  color: white;
  padding-top: 3rem;
  padding-bottom: 1rem;
}

.footer a {
  color: #adb5bd;
  text-decoration: none;
  transition: color 0.2s;
}

.footer a:hover {
  color: white;
}

/* Enhanced Tab Styling */
.nav-pills .nav-link {
  background: rgba(255, 255, 255, 0.1);
  color: #666;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  font-weight: 500;
}

.nav-pills .nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #333;
  transform: translateY(-2px);
}

.nav-pills .nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  border-color: #667eea;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Tab Content Animation */
.tab-pane {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  display: none;
}

.tab-pane.active {
  opacity: 1;
  transform: translateY(0);
  display: block;
}

.tab-pane.show {
  display: block;
}
