{"version": 3, "file": "queryProcessor.js", "sourceRoot": "", "sources": ["../src/queryProcessor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAsB,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACzE,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AASpC,8DAA8D;AAC9D,MAAM,YAAY,GAAyC;IACzD,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE;QAC7B,WAAW,EAAE,uBAAuB;QACpC,cAAc,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;QACrD,KAAK,CAAC,YAAY,CAAC,KAAK;YACtB,OAAO;gBACL,kBAAkB,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE;gBAC5C,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,MAAM;gBAC9B,aAAa,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK;gBACpC,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE;aAC/C,CAAC;QACJ,CAAC;KACF;IAED,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE;QAChC,WAAW,EAAE,iBAAiB;QAC9B,cAAc,EAAE,CAAC,uBAAuB,EAAE,4BAA4B,CAAC;QACvE,KAAK,CAAC,YAAY,CAAC,KAAK;YACtB,OAAO;gBACL,kBAAkB,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE;gBAC5C,uBAAuB,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE;gBAC1D,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;oBACjD,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK;gBACzB,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;oBAC9C,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,SAAS;gBACb,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE;aACrD,CAAC;QACJ,CAAC;KACF;IAED,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE;QAC5B,WAAW,EAAE,sBAAsB;QACnC,cAAc,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;QACpD,KAAK,CAAC,YAAY,CAAC,KAAK;YACtB,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnE,OAAO;gBACL,MAAM;gBACN,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;oBAC7C,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK;gBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;oBAC3C,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,EAAE;gBACN,WAAW,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE;aACtC,CAAC;QACJ,CAAC;KACF;IAED,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE;QAChC,WAAW,EAAE,4BAA4B;QACzC,cAAc,EAAE,CAAC,uBAAuB,CAAC;QACzC,KAAK,CAAC,YAAY,CAAC,KAAK;YACtB,OAAO;gBACL,kBAAkB,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE;gBAC5C,uBAAuB,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE;gBAC1D,qBAAqB,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;oBACzD,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK;gBACzB,mBAAmB,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;oBACvD,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,EAAE;gBACN,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE;aAC/C,CAAC;QACJ,CAAC;KACF;CACF,CAAC;AAEF,wDAAwD;AACxD,MAAM,CAAC,KAAK,UAAU,YAAY,CAChC,aAA4B;IAO5B,MAAM,OAAO,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAEjD,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CAAC,yCAAyC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;KAChF;IAED,IAAI;QACF,4CAA4C;QAC5C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAEzD,wBAAwB;QACxB,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC;QAE1E,kEAAkE;QAClE,MAAM,oBAAoB,GAAwB,EAAE,CAAC;QACrD,IAAI,OAAO,CAAC,cAAc,IAAI,aAAa,CAAC,UAAU,GAAG,GAAG,EAAE;YAC5D,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,cAAc,EAAE;gBACzC,IAAI;oBACF,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC;iBACtE;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,KAAK,CAAC,gCAAgC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;iBAC/D;aACF;SACF;QAED,6EAA6E;QAC7E,MAAM,iBAAiB,GAAG,yBAAyB,CAAC,aAAa,CAAC,CAAC;QAEnE,OAAO;YACL,UAAU;YACV,oBAAoB,EAAE,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,MAAM,GAAG,CAAC;gBAChE,CAAC,CAAC,oBAAoB;gBACtB,CAAC,CAAC,SAAS;YACb,UAAU,EAAE,aAAa,CAAC,UAAU;YACpC,iBAAiB;SAClB,CAAC;KAEH;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAED,iDAAiD;AACjD,SAAS,yBAAyB,CAAC,KAAoB;IACrD,MAAM,SAAS,GAAa,EAAE,CAAC;IAE/B,6CAA6C;IAC7C,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE;QACvB,SAAS,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;KAC7D;IAED,IAAI,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE;QAC9B,SAAS,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;KACvE;IAED,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC,cAAc,EAAE;QAClE,SAAS,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;KAC/D;IAED,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC,cAAc,EAAE;QACpE,SAAS,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;KAC1D;IAED,IAAI,KAAK,CAAC,WAAW,EAAE;QACrB,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;KACtC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,iBAAiB;AACjB,4EAA4E;AAC5E,8CAA8C;AAC9C,gDAAgD"}