{"name": "travel-hub-bd", "version": "1.0.0", "description": "Travel Hub Bangladesh - Flight Booking System with Amadeus API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "build": "npm install", "db:setup": "node scripts/setupDatabase.js", "db:reset": "node scripts/setupDatabase.js", "mcp:database": "node mcp/databaseMCP.js"}, "keywords": ["travel", "flight", "booking", "amadeus", "bangladesh"], "author": "TravelHub BD", "license": "MIT", "dependencies": {"amadeus": "^7.0.0", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "sqlite3": "^5.1.7"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}