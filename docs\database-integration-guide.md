# 🗄️ Database Integration Guide - Travel Hub BD

## 📋 Overview

আপনার Travel Hub BD প্রোজেক্টে সফলভাবে **MySQL Database** এবং **Database MCP Server** integrate করা হয়েছে! এই guide এ সব features এবং usage instructions দেওয়া আছে।

## 🎯 What's Implemented

### ✅ **Database Features:**
- **SQLite Database** (Development)
- **MySQL Support** (Production Ready)
- **Complete User Management**
- **Flight Search History**
- **Booking Management**
- **Price Alerts System**
- **User Preferences**
- **Analytics & Reporting**

### ✅ **MCP Server Features:**
- **Database MCP Server** with 8 tools
- **REST API Endpoints**
- **Real-time Data Sync**
- **Frontend Integration**
- **Health Monitoring**

## 🏗️ Database Structure

```
📊 Database Tables:
├── users (User accounts & profiles)
├── user_preferences (Travel preferences)
├── flight_searches (Search history)
├── bookings (Flight bookings)
├── price_alerts (Price monitoring)
└── search_history (Analytics data)
```

## 🔧 API Endpoints

### **Database Health**
```
GET /api/database/health
```

### **User Management**
```
GET /api/database/users/{userId}/preferences
PUT /api/database/users/{userId}/preferences
GET /api/database/users/{userId}/searches
GET /api/database/users/{userId}/bookings
GET /api/database/users/{userId}/price-alerts
```

### **Search & Analytics**
```
POST /api/database/searches
GET /api/database/analytics/popular-routes
GET /api/database/analytics/{type}
```

### **Price Alerts**
```
POST /api/database/price-alerts
```

## 🛠️ MCP Tools Available

### 1. **search_flights_history**
```json
{
  "user_id": 1,
  "limit": 10,
  "origin": "DAC",
  "destination": "DXB"
}
```

### 2. **get_user_preferences**
```json
{
  "user_id": 1
}
```

### 3. **save_search_data**
```json
{
  "user_id": 1,
  "search_params": {...},
  "results_count": 15,
  "search_duration": 2500
}
```

### 4. **get_popular_routes**
```json
{
  "limit": 10,
  "days": 30
}
```

### 5. **create_price_alert**
```json
{
  "user_id": 1,
  "origin_code": "DAC",
  "destination_code": "DXB",
  "target_price": 75000
}
```

### 6. **get_user_bookings**
```json
{
  "user_id": 1,
  "status": "confirmed",
  "limit": 10
}
```

### 7. **get_analytics_data**
```json
{
  "type": "searches",
  "days": 30
}
```

### 8. **update_user_preferences**
```json
{
  "user_id": 1,
  "preferences": {...}
}
```

## 🚀 Getting Started

### **1. Database Setup**
```bash
# Setup database with sample data
npm run db:setup

# Reset database (if needed)
npm run db:reset
```

### **2. Start Server**
```bash
# Start main server
npm start

# Server will run on: http://localhost:8080
```

### **3. Start MCP Server**
```bash
# Start Database MCP Server
npm run mcp:database
```

### **4. Test Integration**
```
# Visit test page
http://localhost:8080/pages/databaseTest.html

# Main application
http://localhost:8080/pages/ticketBooking.html
```

## 📱 Frontend Integration

### **Automatic Features:**
- ✅ **Search data auto-saving**
- ✅ **User preferences loading**
- ✅ **Popular routes display**
- ✅ **Database health monitoring**

### **Manual Integration:**
```javascript
// Access database integration
const db = window.databaseIntegration;

// Save search data
await db.saveSearchData(searchParams, resultsCount);

// Get user preferences
const prefs = await db.getUserPreferences(userId);

// Create price alert
await db.createPriceAlert(alertData);
```

## 🔄 Database Configuration

### **Development (SQLite)**
```env
DB_DIALECT=sqlite
DB_STORAGE=./database/travel_hub_bd.sqlite
```

### **Production (MySQL)**
```env
DB_DIALECT=mysql
DB_HOST=localhost
DB_PORT=3306
DB_NAME=travel_hub_bd
DB_USER=root
DB_PASSWORD=your_password
```

## 📊 Sample Data

### **Demo User:**
- **Email:** <EMAIL>
- **Password:** demo123456
- **Features:** Full preferences, search history, price alerts

### **Sample Routes:**
- DAC → DXB (Dhaka to Dubai)
- DAC → LHR (Dhaka to London)
- DAC → KUL (Dhaka to Kuala Lumpur)

## 🔍 Testing & Monitoring

### **Health Check:**
```bash
curl http://localhost:8080/api/database/health
```

### **Database Stats:**
```json
{
  "success": true,
  "database": "connected",
  "stats": {
    "users": 1,
    "searches": 1,
    "bookings": 0,
    "price_alerts": 1
  }
}
```

## 🎨 UI Features

### **Enhanced Flight Search:**
- Auto-saves search data
- Loads user preferences
- Shows popular routes
- Tracks search analytics

### **Database Test Page:**
- Health monitoring
- API testing
- Sample data creation
- Analytics visualization

## 🔧 Advanced Configuration

### **MCP Server Config (.roo/mcp.json):**
```json
{
  "mcpServers": {
    "amadeus": {
      "command": "node",
      "args": ["amadeus-mcp-server-standalone/build/index.js"],
      "env": {
        "AMADEUS_CLIENT_ID": "your_client_id",
        "AMADEUS_CLIENT_SECRET": "your_client_secret"
      }
    },
    "database": {
      "command": "node",
      "args": ["mcp/databaseMCP.js"],
      "env": {
        "DB_HOST": "localhost",
        "DB_DIALECT": "sqlite"
      }
    }
  }
}
```

## 🚨 Troubleshooting

### **Common Issues:**

1. **Database Connection Failed**
   ```bash
   # Check if MySQL is running
   # Verify credentials in .env file
   # Use SQLite for development
   ```

2. **MCP Server Not Starting**
   ```bash
   # Install MCP SDK
   npm install @modelcontextprotocol/sdk
   
   # Check database connection
   npm run db:setup
   ```

3. **Port Already in Use**
   ```bash
   # Kill existing process
   taskkill /PID <process_id> /F
   
   # Or change port in .env
   PORT=3000
   ```

## 📈 Next Steps

### **Recommended Enhancements:**
1. **User Authentication System**
2. **Payment Gateway Integration**
3. **Email Notification Service**
4. **Advanced Analytics Dashboard**
5. **Mobile App API**

### **Additional MCP Servers:**
1. **Weather MCP Server**
2. **Currency MCP Server**
3. **Maps MCP Server**
4. **Email MCP Server**

## 🎉 Success!

আপনার Travel Hub BD এখন একটি complete database-driven application! 

### **Key Benefits:**
- ✅ **Persistent Data Storage**
- ✅ **User Personalization**
- ✅ **Search Analytics**
- ✅ **Price Monitoring**
- ✅ **Booking Management**
- ✅ **MCP Integration**

### **Ready for Production:**
- ✅ **Scalable Architecture**
- ✅ **RESTful APIs**
- ✅ **Database Optimization**
- ✅ **Error Handling**
- ✅ **Health Monitoring**

Happy coding! 🚀
