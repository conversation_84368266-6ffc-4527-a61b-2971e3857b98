<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Viewer - Travel Hub BD</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .table-container {
            max-height: 400px;
            overflow-y: auto;
        }
        .json-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
        }
        .table-tab {
            cursor: pointer;
            transition: all 0.3s;
        }
        .table-tab:hover {
            background-color: #e9ecef;
        }
        .table-tab.active {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="mb-0">
                        <i class="fas fa-database text-primary me-3"></i>
                        Database Viewer
                    </h1>
                    <div>
                        <button class="btn btn-success me-2" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                        <a href="ticketBooking.html" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to App
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Stats -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Database Statistics
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="database-stats" class="row">
                            <div class="col-12 text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body p-2">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-outline-primary table-tab active" data-table="users">
                                <i class="fas fa-users me-1"></i>Users
                            </button>
                            <button class="btn btn-outline-primary table-tab" data-table="flight_searches">
                                <i class="fas fa-search me-1"></i>Searches
                            </button>
                            <button class="btn btn-outline-primary table-tab" data-table="bookings">
                                <i class="fas fa-ticket-alt me-1"></i>Bookings
                            </button>
                            <button class="btn btn-outline-primary table-tab" data-table="user_preferences">
                                <i class="fas fa-cog me-1"></i>Preferences
                            </button>
                            <button class="btn btn-outline-primary table-tab" data-table="price_alerts">
                                <i class="fas fa-bell me-1"></i>Alerts
                            </button>
                            <button class="btn btn-outline-primary table-tab" data-table="search_history">
                                <i class="fas fa-history me-1"></i>History
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table Data -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" id="current-table-title">
                            <i class="fas fa-table me-2"></i>Users Table
                        </h5>
                        <span class="badge bg-secondary" id="record-count">0 records</span>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-container">
                            <div id="table-data">
                                <div class="text-center p-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentTable = 'users';
        const API_BASE = '/api/database';

        // Table configurations
        const tableConfigs = {
            users: {
                title: 'Users Table',
                icon: 'fas fa-users',
                endpoint: '/users/1/searches?limit=100', // We'll modify this
                columns: ['id', 'email', 'first_name', 'last_name', 'phone', 'nationality', 'preferred_currency', 'is_verified', 'created_at']
            },
            flight_searches: {
                title: 'Flight Searches',
                icon: 'fas fa-search',
                endpoint: '/users/1/searches?limit=100',
                columns: ['id', 'user_id', 'search_type', 'origin_code', 'destination_code', 'departure_date', 'passengers', 'travel_class', 'created_at']
            },
            bookings: {
                title: 'Bookings',
                icon: 'fas fa-ticket-alt',
                endpoint: '/users/1/bookings?limit=100',
                columns: ['id', 'booking_reference', 'user_id', 'booking_status', 'origin_code', 'destination_code', 'total_price', 'created_at']
            },
            user_preferences: {
                title: 'User Preferences',
                icon: 'fas fa-cog',
                endpoint: '/users/1/preferences',
                columns: ['id', 'user_id', 'preferred_class', 'currency_preference', 'email_notifications', 'price_alert_notifications']
            },
            price_alerts: {
                title: 'Price Alerts',
                icon: 'fas fa-bell',
                endpoint: '/users/1/price-alerts',
                columns: ['id', 'user_id', 'origin_code', 'destination_code', 'target_price', 'currency_code', 'is_active', 'created_at']
            },
            search_history: {
                title: 'Search History',
                icon: 'fas fa-history',
                endpoint: '/analytics/popular-routes',
                columns: ['origin', 'destination', 'search_count']
            }
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadDatabaseStats();
            loadTableData(currentTable);
            setupTableTabs();
        });

        // Setup table tabs
        function setupTableTabs() {
            document.querySelectorAll('.table-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const table = this.dataset.table;
                    switchTable(table);
                });
            });
        }

        // Switch table
        function switchTable(table) {
            currentTable = table;
            
            // Update active tab
            document.querySelectorAll('.table-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-table="${table}"]`).classList.add('active');
            
            // Update title
            const config = tableConfigs[table];
            document.getElementById('current-table-title').innerHTML = 
                `<i class="${config.icon} me-2"></i>${config.title}`;
            
            // Load data
            loadTableData(table);
        }

        // Load database stats
        async function loadDatabaseStats() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.stats;
                    document.getElementById('database-stats').innerHTML = `
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-primary">${stats.users}</h4>
                                <small class="text-muted">Users</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-success">${stats.searches}</h4>
                                <small class="text-muted">Searches</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-warning">${stats.bookings}</h4>
                                <small class="text-muted">Bookings</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-info">${stats.price_alerts}</h4>
                                <small class="text-muted">Price Alerts</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-secondary">${data.database}</h4>
                                <small class="text-muted">Status</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h4 class="text-dark">${new Date(data.timestamp).toLocaleTimeString()}</h4>
                                <small class="text-muted">Last Check</small>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Failed to load stats:', error);
                document.getElementById('database-stats').innerHTML = `
                    <div class="col-12 text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load database statistics
                    </div>
                `;
            }
        }

        // Load table data
        async function loadTableData(table) {
            const config = tableConfigs[table];
            const tableDataDiv = document.getElementById('table-data');
            
            // Show loading
            tableDataDiv.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading ${config.title}...</p>
                </div>
            `;

            try {
                let endpoint = config.endpoint;
                
                // Special handling for different endpoints
                if (table === 'search_history') {
                    endpoint = '/analytics/popular-routes?limit=50';
                }

                const response = await fetch(`${API_BASE}${endpoint}`);
                const result = await response.json();
                
                let data = result.data;
                if (!Array.isArray(data)) {
                    data = [data]; // Convert single object to array
                }

                // Update record count
                document.getElementById('record-count').textContent = `${data.length} records`;

                if (data.length === 0) {
                    tableDataDiv.innerHTML = `
                        <div class="text-center p-4 text-muted">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No data found in ${config.title}</p>
                        </div>
                    `;
                    return;
                }

                // Generate table
                const tableHTML = generateTable(data, config.columns);
                tableDataDiv.innerHTML = tableHTML;

            } catch (error) {
                console.error('Failed to load table data:', error);
                tableDataDiv.innerHTML = `
                    <div class="text-center p-4 text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <p>Failed to load ${config.title}</p>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }

        // Generate table HTML
        function generateTable(data, columns) {
            if (!data || data.length === 0) return '';

            // Use actual data keys if columns not specified
            if (!columns || columns.length === 0) {
                columns = Object.keys(data[0]);
            }

            let html = `
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
            `;

            columns.forEach(col => {
                html += `<th>${col.replace(/_/g, ' ').toUpperCase()}</th>`;
            });

            html += `
                        </tr>
                    </thead>
                    <tbody>
            `;

            data.forEach(row => {
                html += '<tr>';
                columns.forEach(col => {
                    let value = row[col];
                    
                    // Format different data types
                    if (value === null || value === undefined) {
                        value = '<span class="text-muted">NULL</span>';
                    } else if (typeof value === 'object') {
                        value = `<div class="json-data">${JSON.stringify(value, null, 2)}</div>`;
                    } else if (typeof value === 'boolean') {
                        value = value ? '<span class="badge bg-success">True</span>' : '<span class="badge bg-secondary">False</span>';
                    } else if (col.includes('date') || col.includes('time')) {
                        value = new Date(value).toLocaleString();
                    } else if (col.includes('price') || col.includes('amount')) {
                        value = `<span class="text-success">৳${parseFloat(value).toLocaleString()}</span>`;
                    }
                    
                    html += `<td>${value}</td>`;
                });
                html += '</tr>';
            });

            html += `
                    </tbody>
                </table>
            `;

            return html;
        }

        // Refresh data
        function refreshData() {
            loadDatabaseStats();
            loadTableData(currentTable);
        }
    </script>
</body>
</html>
