import { z } from 'zod';
export declare enum TravelQueryType {
    INSPIRATION = "inspiration",
    SPECIFIC_ROUTE = "specific_route",
    MULTI_CITY = "multi_city",
    FLEXIBLE_VALUE = "flexible_value"
}
export declare enum TimeFrame {
    SPECIFIC_DATE = "specific_date",
    DATE_RANGE = "date_range",
    MONTH = "month",
    SEASON = "season",
    HOLIDAY = "holiday",
    RELATIVE = "relative",
    FLEXIBLE = "flexible"
}
export declare enum ClimatePreference {
    WARM = "warm",
    COLD = "cold",
    TROPICAL = "tropical",
    MILD = "mild",
    SUNNY = "sunny",
    ANY = "any"
}
export interface Duration {
    type: 'days' | 'weeks' | 'months' | 'flexible';
    value: number | [number, number];
    isApproximate: boolean;
}
export interface LocationReference {
    raw: string;
    type: 'city' | 'airport' | 'region' | 'country';
    code?: string;
    isFlexible: boolean;
    context?: string;
}
export interface BudgetConstraint {
    amount: number;
    currency: string;
    type: 'total' | 'per_person' | 'per_flight';
    isFlexible: boolean;
    context?: string;
}
export interface TravelPreferences {
    purpose?: 'leisure' | 'business' | 'family' | 'adventure';
    class?: 'economy' | 'premium_economy' | 'business' | 'first';
    stops?: 'direct' | 'any' | number;
    activities?: string[];
    accommodation?: string[];
}
export interface AnalyzedQuery {
    type: TravelQueryType;
    timeFrame: {
        type: TimeFrame;
        value: string | [string, string];
        isFlexible: boolean;
    };
    origin?: LocationReference;
    destinations: LocationReference[];
    duration?: Duration;
    budget?: BudgetConstraint;
    climate?: ClimatePreference;
    preferences?: TravelPreferences;
    rawQuery: string;
    confidence: number;
    ambiguities?: string[];
}
export declare const analyzedQuerySchema: z.ZodObject<{
    type: z.ZodNativeEnum<typeof TravelQueryType>;
    timeFrame: z.ZodObject<{
        type: z.ZodNativeEnum<typeof TimeFrame>;
        value: z.ZodUnion<[z.ZodString, z.ZodTuple<[z.ZodString, z.ZodString], null>]>;
        isFlexible: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        type?: TimeFrame;
        value?: string | [string, string, ...unknown[]];
        isFlexible?: boolean;
    }, {
        type?: TimeFrame;
        value?: string | [string, string, ...unknown[]];
        isFlexible?: boolean;
    }>;
    origin: z.ZodOptional<z.ZodObject<{
        raw: z.ZodString;
        type: z.ZodEnum<["city", "airport", "region", "country"]>;
        code: z.ZodOptional<z.ZodString>;
        isFlexible: z.ZodBoolean;
        context: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        type?: "city" | "airport" | "region" | "country";
        code?: string;
        isFlexible?: boolean;
        raw?: string;
        context?: string;
    }, {
        type?: "city" | "airport" | "region" | "country";
        code?: string;
        isFlexible?: boolean;
        raw?: string;
        context?: string;
    }>>;
    destinations: z.ZodArray<z.ZodObject<{
        raw: z.ZodString;
        type: z.ZodEnum<["city", "airport", "region", "country"]>;
        code: z.ZodOptional<z.ZodString>;
        isFlexible: z.ZodBoolean;
        context: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        type?: "city" | "airport" | "region" | "country";
        code?: string;
        isFlexible?: boolean;
        raw?: string;
        context?: string;
    }, {
        type?: "city" | "airport" | "region" | "country";
        code?: string;
        isFlexible?: boolean;
        raw?: string;
        context?: string;
    }>, "many">;
    duration: z.ZodOptional<z.ZodObject<{
        type: z.ZodEnum<["days", "weeks", "months", "flexible"]>;
        value: z.ZodUnion<[z.ZodNumber, z.ZodTuple<[z.ZodNumber, z.ZodNumber], null>]>;
        isApproximate: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        type?: "days" | "weeks" | "months" | "flexible";
        value?: number | [number, number, ...unknown[]];
        isApproximate?: boolean;
    }, {
        type?: "days" | "weeks" | "months" | "flexible";
        value?: number | [number, number, ...unknown[]];
        isApproximate?: boolean;
    }>>;
    budget: z.ZodOptional<z.ZodObject<{
        amount: z.ZodNumber;
        currency: z.ZodString;
        type: z.ZodEnum<["total", "per_person", "per_flight"]>;
        isFlexible: z.ZodBoolean;
        context: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        amount?: number;
        type?: "total" | "per_person" | "per_flight";
        isFlexible?: boolean;
        context?: string;
        currency?: string;
    }, {
        amount?: number;
        type?: "total" | "per_person" | "per_flight";
        isFlexible?: boolean;
        context?: string;
        currency?: string;
    }>>;
    climate: z.ZodOptional<z.ZodNativeEnum<typeof ClimatePreference>>;
    preferences: z.ZodOptional<z.ZodObject<{
        purpose: z.ZodOptional<z.ZodEnum<["leisure", "business", "family", "adventure"]>>;
        class: z.ZodOptional<z.ZodEnum<["economy", "premium_economy", "business", "first"]>>;
        stops: z.ZodOptional<z.ZodUnion<[z.ZodEnum<["direct", "any"]>, z.ZodNumber]>>;
        activities: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        accommodation: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        stops?: number | "direct" | "any";
        purpose?: "leisure" | "business" | "family" | "adventure";
        class?: "business" | "economy" | "premium_economy" | "first";
        activities?: string[];
        accommodation?: string[];
    }, {
        stops?: number | "direct" | "any";
        purpose?: "leisure" | "business" | "family" | "adventure";
        class?: "business" | "economy" | "premium_economy" | "first";
        activities?: string[];
        accommodation?: string[];
    }>>;
    rawQuery: z.ZodString;
    confidence: z.ZodNumber;
    ambiguities: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    duration?: {
        type?: "days" | "weeks" | "months" | "flexible";
        value?: number | [number, number, ...unknown[]];
        isApproximate?: boolean;
    };
    type?: TravelQueryType;
    origin?: {
        type?: "city" | "airport" | "region" | "country";
        code?: string;
        isFlexible?: boolean;
        raw?: string;
        context?: string;
    };
    budget?: {
        amount?: number;
        type?: "total" | "per_person" | "per_flight";
        isFlexible?: boolean;
        context?: string;
        currency?: string;
    };
    preferences?: {
        stops?: number | "direct" | "any";
        purpose?: "leisure" | "business" | "family" | "adventure";
        class?: "business" | "economy" | "premium_economy" | "first";
        activities?: string[];
        accommodation?: string[];
    };
    timeFrame?: {
        type?: TimeFrame;
        value?: string | [string, string, ...unknown[]];
        isFlexible?: boolean;
    };
    destinations?: {
        type?: "city" | "airport" | "region" | "country";
        code?: string;
        isFlexible?: boolean;
        raw?: string;
        context?: string;
    }[];
    climate?: ClimatePreference;
    rawQuery?: string;
    confidence?: number;
    ambiguities?: string[];
}, {
    duration?: {
        type?: "days" | "weeks" | "months" | "flexible";
        value?: number | [number, number, ...unknown[]];
        isApproximate?: boolean;
    };
    type?: TravelQueryType;
    origin?: {
        type?: "city" | "airport" | "region" | "country";
        code?: string;
        isFlexible?: boolean;
        raw?: string;
        context?: string;
    };
    budget?: {
        amount?: number;
        type?: "total" | "per_person" | "per_flight";
        isFlexible?: boolean;
        context?: string;
        currency?: string;
    };
    preferences?: {
        stops?: number | "direct" | "any";
        purpose?: "leisure" | "business" | "family" | "adventure";
        class?: "business" | "economy" | "premium_economy" | "first";
        activities?: string[];
        accommodation?: string[];
    };
    timeFrame?: {
        type?: TimeFrame;
        value?: string | [string, string, ...unknown[]];
        isFlexible?: boolean;
    };
    destinations?: {
        type?: "city" | "airport" | "region" | "country";
        code?: string;
        isFlexible?: boolean;
        raw?: string;
        context?: string;
    }[];
    climate?: ClimatePreference;
    rawQuery?: string;
    confidence?: number;
    ambiguities?: string[];
}>;
export declare function identifyQueryType(query: string): TravelQueryType;
export declare function extractTimeFrame(query: string): {
    type: TimeFrame;
    value: string | [string, string];
    isFlexible: boolean;
};
export declare function extractLocations(query: string): {
    origin?: LocationReference;
    destinations: LocationReference[];
};
export declare function analyzeQuery(query: string): Promise<AnalyzedQuery>;
