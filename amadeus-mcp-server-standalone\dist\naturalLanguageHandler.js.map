{"version": 3, "file": "naturalLanguageHandler.js", "sourceRoot": "", "sources": ["../src/naturalLanguageHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAUxB,0CAA0C;AAC1C,SAAS,cAAc,CACrB,WAAgB,EAChB,aAAkB;IAElB,MAAM,QAAQ,GAAe;QAC3B,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,WAAW,CAAC,UAAU;QAC/B,gBAAgB,EAAE,EAAE;QACpB,iBAAiB,EAAE,WAAW,CAAC,iBAAiB,IAAI,EAAE;KACvD,CAAC;IAEF,uCAAuC;IACvC,QAAQ,aAAa,CAAC,IAAI,EAAE;QAC1B,KAAK,aAAa;YAChB,QAAQ,CAAC,OAAO,GAAG,mEACjB,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,aAAa,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAC/E,GACE,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,0BAA0B,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EACnF,GAAG,CAAC;YACJ,MAAM;QAER,KAAK,gBAAgB;YACnB,QAAQ,CAAC,OAAO,GAAG,yCAAyC,aAAa,CAAC,MAAM,EAAE,GAAG,OACnF,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GACjC,GAAG,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,aAAa,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;YAClF,MAAM;QAER,KAAK,YAAY;YACf,QAAQ,CAAC,OAAO,GAAG,2CACjB,aAAa,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CACtD,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,aAAa,CAAC,QAAQ,CAAC,KAAK,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;YAC3G,MAAM;QAER,KAAK,gBAAgB;YACnB,QAAQ,CAAC,OAAO,GAAG,8DACjB,aAAa,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EACvF,GAAG,CAAC;YACJ,MAAM;KACT;IAED,8CAA8C;IAC9C,IAAI,WAAW,CAAC,oBAAoB,EAAE;QACpC,QAAQ,CAAC,gBAAgB,GAAG;YAC1B,uCAAuC;YACvC,4BAA4B;YAC5B,qBAAqB;YACrB,kCAAkC;SACnC,CAAC;KACH;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,4CAA4C;AAC5C,MAAM,CAAC,KAAK,UAAU,0BAA0B,CAC9C,KAAa;IAEb,IAAI;QACF,6CAA6C;QAC7C,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,KAAK,CAAC,CAAC;QAE3C,4DAA4D;QAC5D,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE5C,kDAAkD;QAClD,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAElD,4CAA4C;QAC5C,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE;YAC7B,QAAQ,CAAC,OAAO,GAAG,8BAA8B,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,8DAA8D,CAAC;SAC/I;QAED,OAAO,QAAQ,CAAC;KAEjB;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAC/D,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAED,uCAAuC;AACvC,MAAM,CAAC,MAAM,CACX,yBAAyB,EACzB,wCAAwC,EACxC;IACE,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;CAChE,EACD,KAAK,EAAE,EAAE,KAAK,EAAqB,EAAE,EAAE;IACrC,MAAM,QAAQ,GAAG,MAAM,0BAA0B,CAAC,KAAK,CAAC,CAAC;IAEzD,OAAO;QACL,QAAQ,EAAE;YACR;gBACE,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE;oBACP,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,GAAG,QAAQ,CAAC,OAAO,OACvB,QAAQ,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC;wBACnC,CAAC,CAAC,iEAAiE,QAAQ,CAAC,iBAAiB;6BACxF,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;6BAClB,IAAI,CAAC,IAAI,CAAC,MAAM;wBACrB,CAAC,CAAC,EACN,GACE,QAAQ,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC;wBAClC,CAAC,CAAC,kBAAkB,QAAQ,CAAC,gBAAgB;6BACxC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;6BAClB,IAAI,CAAC,IAAI,CAAC,EAAE;wBACjB,CAAC,CAAC,EACN,EAAE;iBACH;aACF;SACF;KACF,CAAC;AACJ,CAAC,CACF,CAAC"}