const { Sequelize, DataTypes } = require("sequelize");
const { sequelize } = require("../config/database");

// Import all models
const User = require("./User")(sequelize, DataTypes);
const FlightSearch = require("./FlightSearch")(sequelize, DataTypes);
const Booking = require("./Booking")(sequelize, DataTypes);
const UserPreference = require("./UserPreference")(sequelize, DataTypes);
const PriceAlert = require("./PriceAlert")(sequelize, DataTypes);
const SearchHistory = require("./SearchHistory")(sequelize, DataTypes);
const Contact = require("./Contact")(sequelize, DataTypes);

// Define associations
const models = {
  User,
  FlightSearch,
  Booking,
  UserPreference,
  PriceAlert,
  SearchHistory,
  Contact,
};

// User associations
User.hasMany(FlightSearch, { foreignKey: "user_id", as: "searches" });
User.hasMany(Booking, { foreignKey: "user_id", as: "bookings" });
User.hasOne(UserPreference, { foreignKey: "user_id", as: "preferences" });
User.hasMany(PriceAlert, { foreignKey: "user_id", as: "priceAlerts" });
User.hasMany(SearchHistory, { foreignKey: "user_id", as: "searchHistory" });
User.hasMany(Contact, { foreignKey: "user_id", as: "contacts" });

// FlightSearch associations
FlightSearch.belongsTo(User, { foreignKey: "user_id", as: "user" });
FlightSearch.hasMany(Booking, { foreignKey: "search_id", as: "bookings" });

// Booking associations
Booking.belongsTo(User, { foreignKey: "user_id", as: "user" });
Booking.belongsTo(FlightSearch, { foreignKey: "search_id", as: "search" });

// UserPreference associations
UserPreference.belongsTo(User, { foreignKey: "user_id", as: "user" });

// PriceAlert associations
PriceAlert.belongsTo(User, { foreignKey: "user_id", as: "user" });

// SearchHistory associations
SearchHistory.belongsTo(User, { foreignKey: "user_id", as: "user" });

// Contact associations
Contact.belongsTo(User, { foreignKey: "user_id", as: "user" });

// Add sequelize instance to models
Object.keys(models).forEach((modelName) => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

models.sequelize = sequelize;
models.Sequelize = Sequelize;

module.exports = models;
