const { Sequelize } = require("sequelize");
require("dotenv").config();

// Database configuration
const config = {
  development: {
    dialect: process.env.DB_DIALECT || "sqlite",
    storage: process.env.DB_STORAGE || "./database/travel_hub_bd.sqlite",
    // MySQL config (if using MySQL)
    username: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "travel_hub_bd",
    host: process.env.DB_HOST || "localhost",
    port: process.env.DB_PORT || 3306,
    logging: console.log, // Enable SQL logging in development
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  },
  test: {
    username: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME + "_test" || "travel_hub_bd_test",
    host: process.env.DB_HOST || "localhost",
    port: process.env.DB_PORT || 3306,
    dialect: process.env.DB_DIALECT || "mysql",
    logging: false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  },
  production: {
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: process.env.DB_DIALECT,
    logging: false,
    pool: {
      max: 20,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  },
};

// Create Sequelize instance
const env = process.env.NODE_ENV || "development";
const sequelize = new Sequelize(config[env]);

// Test database connection
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log("✅ Database connection established successfully.");
    return true;
  } catch (error) {
    console.error("❌ Unable to connect to the database:", error.message);
    return false;
  }
}

// Initialize database
async function initializeDatabase() {
  try {
    // Test connection first
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error("Database connection failed");
    }

    // Sync all models (force: true for development to recreate tables)
    const isDevelopment =
      process.env.NODE_ENV === "development" || !process.env.NODE_ENV;
    await sequelize.sync({
      force: isDevelopment, // Recreate tables in development
      alter: !isDevelopment, // Only alter in production
    });
    console.log("✅ Database synchronized successfully.");

    return sequelize;
  } catch (error) {
    console.error("❌ Database initialization failed:", error.message);
    throw error;
  }
}

module.exports = {
  sequelize,
  config,
  testConnection,
  initializeDatabase,
};
