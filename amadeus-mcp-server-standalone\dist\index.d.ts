import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
type TypedCache = {
    get: <T>(key: string) => T | undefined;
    set: <T>(key: string, value: T, ttl?: number) => boolean;
};
export declare let amadeus: any;
export declare const server: McpServer;
export declare const cache: TypedCache;
/**
 * Wrapper for Amadeus API calls with caching
 * @param cacheKey - Key for caching
 * @param ttl - Time to live in seconds
 * @param apiCall - Function that returns a promise with the API call
 * @returns Promise with API response
 */
export declare function cachedApiCall<T>(cacheKey: string, ttl: number, apiCall: () => Promise<T>): Promise<T>;
export declare function main(): Promise<void>;
export {};
