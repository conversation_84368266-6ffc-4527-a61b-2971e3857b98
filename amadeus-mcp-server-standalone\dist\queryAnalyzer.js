import { z } from 'zod';
// Types of travel queries we can handle
export var TravelQueryType;
(function (TravelQueryType) {
    TravelQueryType["INSPIRATION"] = "inspiration";
    TravelQueryType["SPECIFIC_ROUTE"] = "specific_route";
    TravelQueryType["MULTI_CITY"] = "multi_city";
    TravelQueryType["FLEXIBLE_VALUE"] = "flexible_value";
})(TravelQueryType = TravelQueryType || (TravelQueryType = {}));
// Time expressions in queries
export var TimeFrame;
(function (TimeFrame) {
    TimeFrame["SPECIFIC_DATE"] = "specific_date";
    TimeFrame["DATE_RANGE"] = "date_range";
    TimeFrame["MONTH"] = "month";
    TimeFrame["SEASON"] = "season";
    TimeFrame["HOLIDAY"] = "holiday";
    TimeFrame["RELATIVE"] = "relative";
    TimeFrame["FLEXIBLE"] = "flexible";
})(TimeFrame = TimeFrame || (TimeFrame = {}));
// Weather/Climate preferences
export var ClimatePreference;
(function (ClimatePreference) {
    ClimatePreference["WARM"] = "warm";
    ClimatePreference["COLD"] = "cold";
    ClimatePreference["TROPICAL"] = "tropical";
    ClimatePreference["MILD"] = "mild";
    ClimatePreference["SUNNY"] = "sunny";
    ClimatePreference["ANY"] = "any";
})(ClimatePreference = ClimatePreference || (ClimatePreference = {}));
// Zod schema for validation
export const analyzedQuerySchema = z.object({
    type: z.nativeEnum(TravelQueryType),
    timeFrame: z.object({
        type: z.nativeEnum(TimeFrame),
        value: z.union([z.string(), z.tuple([z.string(), z.string()])]),
        isFlexible: z.boolean(),
    }),
    origin: z.object({
        raw: z.string(),
        type: z.enum(['city', 'airport', 'region', 'country']),
        code: z.string().optional(),
        isFlexible: z.boolean(),
        context: z.string().optional(),
    }).optional(),
    destinations: z.array(z.object({
        raw: z.string(),
        type: z.enum(['city', 'airport', 'region', 'country']),
        code: z.string().optional(),
        isFlexible: z.boolean(),
        context: z.string().optional(),
    })),
    duration: z.object({
        type: z.enum(['days', 'weeks', 'months', 'flexible']),
        value: z.union([z.number(), z.tuple([z.number(), z.number()])]),
        isApproximate: z.boolean(),
    }).optional(),
    budget: z.object({
        amount: z.number(),
        currency: z.string(),
        type: z.enum(['total', 'per_person', 'per_flight']),
        isFlexible: z.boolean(),
        context: z.string().optional(),
    }).optional(),
    climate: z.nativeEnum(ClimatePreference).optional(),
    preferences: z.object({
        purpose: z.enum(['leisure', 'business', 'family', 'adventure']).optional(),
        class: z.enum(['economy', 'premium_economy', 'business', 'first']).optional(),
        stops: z.union([z.enum(['direct', 'any']), z.number()]).optional(),
        activities: z.array(z.string()).optional(),
        accommodation: z.array(z.string()).optional(),
    }).optional(),
    rawQuery: z.string(),
    confidence: z.number().min(0).max(1),
    ambiguities: z.array(z.string()).optional(),
});
// Helper function to identify query type
export function identifyQueryType(query) {
    const lowercaseQuery = query.toLowerCase();
    // Inspiration patterns
    if (lowercaseQuery.includes('where can i go') ||
        lowercaseQuery.includes('suggest') ||
        lowercaseQuery.includes('recommend')) {
        return TravelQueryType.INSPIRATION;
    }
    // Multi-city patterns
    if (lowercaseQuery.includes('visit') ||
        lowercaseQuery.includes('multiple cities') ||
        (lowercaseQuery.match(/,/g) || []).length >= 2) {
        return TravelQueryType.MULTI_CITY;
    }
    // Flexible value patterns
    if (lowercaseQuery.includes('cheapest time') ||
        lowercaseQuery.includes('best time') ||
        lowercaseQuery.includes('when should')) {
        return TravelQueryType.FLEXIBLE_VALUE;
    }
    // Default to specific route
    return TravelQueryType.SPECIFIC_ROUTE;
}
// Helper function to extract time frame
export function extractTimeFrame(query) {
    // This is a placeholder - would need more sophisticated date parsing
    return {
        type: TimeFrame.FLEXIBLE,
        value: '',
        isFlexible: true,
    };
}
// Helper function to extract locations
export function extractLocations(query) {
    // This is a placeholder - would need location database and parsing
    return {
        destinations: [],
    };
}
// Main analysis function
export async function analyzeQuery(query) {
    const type = identifyQueryType(query);
    const timeFrame = extractTimeFrame(query);
    const locations = extractLocations(query);
    const analysis = {
        type,
        timeFrame,
        ...locations,
        rawQuery: query,
        confidence: 0.8, // This should be calculated based on certainty of parsing
    };
    // Validate the analysis
    analyzedQuerySchema.parse(analysis);
    return analysis;
}
//# sourceMappingURL=queryAnalyzer.js.map